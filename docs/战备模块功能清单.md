# 战备器材管理模块 - 功能清单文档

## 模块概述
实现战备、装备器材在位、借出、补入、保养记录的登记功能，提供完整的战备物资管理解决方案。

---


## 2. MaterialController - 物资台账管理（库存）

### 2.1 物资信息管理
- **分页查询物资台账** `GET /api/material/page`
  - 支持关键词搜索（物资名称/编码）
  - 支持分类筛选
  - 支持库室筛选
  - 支持状态筛选

- **创建物资信息** `POST /api/material`
  - 录入物资基本信息（编码、名称、规格、单位）
  - 设置物资分类
  - 设置品牌型号信息

- **更新物资信息** `PUT /api/material/{id}`
  - 修改物资基本信息
  - 更新物资状态

- **删除物资信息** `DELETE /api/material/{id}`
  - 物资信息删除（需检查是否有库存）

- **获取物资详情** `GET /api/material/{id}`
  - 显示物资完整信息
  - 显示关联的库存信息

- **分页查询库存信息**
  - 支持关键词搜索（物资名称/编码）
  - 支持物资ID筛选
  - 支持库房ID筛选
  - 支持分类ID筛选
  - 支持库存状态筛选（normal/low/zero）

- **获取物资总库存**
  - 获取所有物资的总库存信息
  - 包含所有库房的库存汇总

- **检查库存充足性**
  - 检查指定物资在指定库房的库存是否充足

 装备器材在位监控
- **在位状态查询** 
  - 实时查询装备器材在位情况


### 2.2 物资分类管理（合并MaterialCategoryController）
- **获取分类树形结构** `GET /api/material/category/tree`
  - 完整的分类树形结构
  - 支持前端树形组件展示

- **获取分类列表** `GET /api/material/category/list`
  - 平铺的分类列表
  - 按层级排序

- **创建分类** `POST /api/material/category`
  - 新增物资分类
  - 支持多级分类

- **更新分类** `PUT /api/material/category/{id}`
  - 修改分类信息

- **删除分类** `DELETE /api/material/category/{id}`
  - 删除分类（需检查是否有关联物资）

### 2.3 资源实力统计
- **物资统计概览** `GET /api/material/statistics`
  - 物资种类总数统计
  - 分类物资数量分布
  - 物资状态分布统计

- **物资台账导出** `GET /api/material/export`
  - 导出完整物资台账
  - 支持Excel格式导出
---

## 3. InboundController - 物资入库管理（补入）

1.获取库室列表
描述：获取所有可选库室信息

2.查询库室物资
描述：根据库室ID获取该库室下的所有物资信息

3.提交入库单
描述：执行物资上架入库操作

4.入库记录分页与条件查询
描述：查询历史入库记录


## 4. OutboundController - 物资出库管理（借出）

1.创建出库单
描述：创建新的物资出库工单（接口内包含选择出库类型、选择接口单位、接收人、选择出库物资、数量及库存检查）

2.查询出库单列表
描述：获取出库工单历史记录

3.获取出库单详情
描述：根据ID查询出库单详细信息

4.分页查询出库记录


## 5. StockController（名字换一下） - 库存盘点

 库存盘点管理
- **创建盘点单** `POST /api/stock/inventory/create`
  - 生成库存盘点工单
  - 选择盘点范围（全盘/部分盘点）
  - 设置盘点人员

- **盘点信息查询** `GET /api/stock/inventory/{id}`
  - 通过盘点单生成的物资名称、数量、位置等信息
  - 供盘点人员对照实际物资进行核查

- **更新盘点结果** `POST /api/stock/inventory/update`
  - 录入实际盘点数量
  - 对核查后的数据进行更新

- **完成盘点** `POST /api/stock/inventory/{orderId}/complete`
  - 完成盘点并调整库存
  - 保证库存数量正确
---

## 6. MaintenanceController - 保养记录管理

### 6.1 保养记录管理
- **分页查询保养记录** `GET /api/maintenance/page`
  - 保养记录列表查询
  - 支持物资筛选
  - 支持时间范围查询

- **手工记录保养** `POST /api/maintenance`
  - 录入物资保养情况
  - 记录保养内容和结果
  - 上传保养图片

- **更新保养记录** `PUT /api/maintenance/{id}`
  - 修改保养记录信息

- **删除保养记录** `DELETE /api/maintenance/{id}`
  - 删除保养记录

- **保养记录详情** `GET /api/maintenance/{id}`
  - 显示保养记录详细信息

### 6.2 历史保养记录查询
- **查看历史保养记录** `GET /api/maintenance/history`
  - 支持按物资查看历史保养记录情况
  - 支持按时间查询

- **保养报表导出** `GET /api/maintenance/export`
  - 导出保养记录报表
  - 支持Excel格式

