package com.fadun.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;


/**
 * Knife4j配置类
 * 用于配置API文档生成和展示
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
// @Configuration  // 暂时禁用Swagger，解决兼容性问题
// @EnableSwagger2
public class Knife4jConfig {

    /**
     * 创建API文档
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                // 扫描指定包下的Controller
                .apis(RequestHandlerSelectors.basePackage("com.fadun.controller"))
                // 扫描所有路径
                .paths(PathSelectors.any())
                .build()
                // 解决Spring Boot 2.6+兼容性问题
                .pathMapping("/");
    }

    /**
     * API信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("法盾项目API文档")
                .description("法盾项目的RESTful API接口文档")
                .version("1.0.0")
                .contact(new Contact("法盾团队", "", ""))
                .build();
    }
}
