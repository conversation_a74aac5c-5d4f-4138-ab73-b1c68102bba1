package com.fadun.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;


/**
 * OpenAPI配置类
 * 用于配置API文档生成和展示
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class OpenApiConfig {

    /**
     * 创建API文档
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("法盾项目API文档")
                        .description("法盾项目的RESTful API接口文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("法盾团队")
                                .email("")
                                .url("")));
    }
}
