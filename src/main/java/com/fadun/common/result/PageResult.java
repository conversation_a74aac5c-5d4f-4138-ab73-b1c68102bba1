package com.fadun.common.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Page Result
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ApiModel(description = "Page Result")
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Current Page", example = "1")
    private Long current;

    @ApiModelProperty(value = "Page Size", example = "10")
    private Long size;

    @ApiModelProperty(value = "Total Records", example = "100")
    private Long total;

    @ApiModelProperty(value = "Total Pages", example = "10")
    private Long pages;

    @ApiModelProperty(value = "Data List")
    private List<T> records;

    public PageResult() {
    }

    public PageResult(Long current, Long size, Long total, List<T> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.records = records;
        this.pages = (total + size - 1) / size;
    }

    /**
     * Create page result
     */
    public static <T> PageResult<T> of(Long current, Long size, Long total, List<T> records) {
        return new PageResult<>(current, size, total, records);
    }

    /**
     * Create empty page result
     */
    public static <T> PageResult<T> empty(Long current, Long size) {
        return new PageResult<>(current, size, 0L, null);
    }

    /**
     * Convert from MyBatis Plus Page object
     */
    public static <T> PageResult<T> fromPage(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        return new PageResult<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    /**
     * Convert data type
     */
    public <R> PageResult<R> convert(List<R> newRecords) {
        PageResult<R> result = new PageResult<>();
        result.setCurrent(this.current);
        result.setSize(this.size);
        result.setTotal(this.total);
        result.setPages(this.pages);
        result.setRecords(newRecords);
        return result;
    }
}