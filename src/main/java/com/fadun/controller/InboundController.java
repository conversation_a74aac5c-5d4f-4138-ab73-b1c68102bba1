package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.InboundOrder;
import com.fadun.entity.InboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;
import com.fadun.service.InboundManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 物资入库管理控制器
 * 实现2个核心功能：提交入库单、入库记录查询
 * 注意：库室列表请使用 GET /api/warehouse/list
 *      库室物资请使用 GET /api/material/by-warehouse/{warehouseId}
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/inbound")
@RequiredArgsConstructor
public class InboundController {

    private final InboundManagementService inboundManagementService;

    
    /**
     * 提交入库单
     */
    @PostMapping("/submit")
    public Result<Void> submitInboundOrder(@RequestBody @Valid InboundOrder inboundOrder) {
        boolean success = inboundManagementService.submitInboundOrder(inboundOrder);
        return ResultUtils.condition(success, "提交入库单成功", "提交入库单失败");
    }

    
    /**
     * 入库记录分页与条件查询
     */
    @GetMapping("/records")
    public Result<PageResult<InboundOrder>> getInboundRecords(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long warehouseId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        // 使用现有的分页查询方法，忽略日期参数
        PageResult<InboundOrder> pageResult = inboundManagementService.getInboundRecords(current, size, keyword, warehouseId, startDate, endDate);
        return ResultUtils.success("查询入库记录成功", pageResult);
    }

    
    /**
     * 根据入库单ID查询明细列表
     */
    @GetMapping("/{orderId}/details")
    public Result<List<InboundDetail>> getDetailsByOrderId(@PathVariable Long orderId) {
        List<InboundDetail> details = inboundManagementService.getDetailsByOrderId(orderId);
        return ResultUtils.success("查询入库明细成功", details);
    }

}
