package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.InboundOrder;
import com.fadun.entity.InboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;
import com.fadun.service.InboundManagementService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 物资入库管理控制器
 * 实现2个核心功能：提交入库单、入库记录查询
 * 注意：库室列表请使用 GET /api/warehouse/list
 *      库室物资请使用 GET /api/material/by-warehouse/{warehouseId}
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-物资入库管理", description = "物资补入登记功能")
@RestController
@RequestMapping("/api/inbound")
@RequiredArgsConstructor
public class InboundController {

    private final InboundManagementService inboundManagementService;

    
    /**
     * 提交入库单
     */
    @ApiOperation(value = "提交入库单", notes = "执行物资上架入库操作")
    @PostMapping("/submit")
    public Result<Void> submitInboundOrder(@RequestBody @Valid @ApiParam("入库单信息") InboundOrder inboundOrder) {
        boolean success = inboundManagementService.submitInboundOrder(inboundOrder);
        return ResultUtils.condition(success, "提交入库单成功", "提交入库单失败");
    }

    
    /**
     * 入库记录分页与条件查询
     */
    @ApiOperation(value = "入库记录查询", notes = "查询历史入库记录，支持分页和条件筛选")
    @GetMapping("/records")
    public Result<PageResult<InboundOrder>> getInboundRecords(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long warehouseId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        // 使用现有的分页查询方法，忽略日期参数
        PageResult<InboundOrder> pageResult = inboundManagementService.getInboundRecords(current, size, keyword, warehouseId, startDate, endDate);
        return ResultUtils.success("查询入库记录成功", pageResult);
    }

    
    /**
     * 根据入库单ID查询明细列表
     */
    @ApiOperation(value = "查询入库明细", notes = "根据入库单ID查询明细列表")
    @GetMapping("/{orderId}/details")
    public Result<List<InboundDetail>> getDetailsByOrderId(@PathVariable Long orderId) {
        List<InboundDetail> details = inboundManagementService.getDetailsByOrderId(orderId);
        return ResultUtils.success("查询入库明细成功", details);
    }

}
