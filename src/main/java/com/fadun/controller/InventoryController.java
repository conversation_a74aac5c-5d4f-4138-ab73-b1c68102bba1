package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.InventoryOrder;
import com.fadun.entity.InventoryDetail;
import com.fadun.service.InventoryManagementService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 库存盘点控制器
 * 专注于库存盘点管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-库存盘点", description = "库存盘点管理")
@RestController
@RequestMapping("/api/inventory")
@RequiredArgsConstructor
public class InventoryController {

    private final InventoryManagementService inventoryManagementService;

    // ========== 盘点单管理 ==========

    /**
     * 分页查询盘点单
     */
    @ApiOperation(value = "分页查询盘点单", notes = "支持关键词搜索、库室筛选、盘点类型筛选、状态筛选")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "current", value = "当前页", dataType = "long", paramType = "query"),
        @ApiImplicitParam(name = "size", value = "每页大小", dataType = "long", paramType = "query"),
        @ApiImplicitParam(name = "keyword", value = "关键词", dataType = "string", paramType = "query"),
        @ApiImplicitParam(name = "warehouseId", value = "库室ID", dataType = "long", paramType = "query"),
        @ApiImplicitParam(name = "inventoryType", value = "盘点类型", dataType = "string", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态", dataType = "int", paramType = "query")
    })
    @GetMapping("/orders")
    public Result<PageResult<InventoryOrder>> getInventoryOrderPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long warehouseId,
            @RequestParam(required = false) String inventoryType,
            @RequestParam(required = false) Integer status) {
        PageResult<InventoryOrder> result = inventoryManagementService.getInventoryOrderPage(
                current, size, keyword, warehouseId, inventoryType, status);
        return ResultUtils.success("查询成功", result);
    }

    /**
     * 创建盘点单
     */
    @ApiOperation(value = "创建盘点单", notes = "生成库存盘点工单，选择盘点范围（全盘/部分盘点），设置盘点人员")
    @PostMapping("/orders")
    public Result<Void> createInventoryOrder(@RequestBody @Valid @ApiParam("盘点单信息") InventoryOrder inventoryOrder) {
        boolean success = inventoryManagementService.createInventoryOrder(inventoryOrder);
        return ResultUtils.condition(success, "创建盘点单成功", "创建盘点单失败");
    }

    /**
     * 获取盘点单详情
     */
    @ApiOperation(value = "获取盘点单详情", notes = "获取盘点单基本信息，包含库室信息")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/orders/{orderId}")
    public Result<InventoryOrder> getInventoryOrderDetail(@PathVariable Long orderId) {
        InventoryOrder inventoryOrder = inventoryManagementService.getInventoryOrderDetail(orderId);
        return ResultUtils.success("获取盘点单详情成功", inventoryOrder);
    }

    /**
     * 开始盘点
     */
    @ApiOperation(value = "开始盘点", notes = "根据选择的物资生成盘点明细")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/orders/{orderId}/start")
    public Result<Void> startInventory(@PathVariable Long orderId, 
                                      @RequestBody @ApiParam("物资ID列表") List<Long> materialIds) {
        boolean success = inventoryManagementService.startInventory(orderId, materialIds);
        return ResultUtils.condition(success, "开始盘点成功", "开始盘点失败");
    }

    /**
     * 完成盘点
     */
    @ApiOperation(value = "完成盘点", notes = "完成盘点并调整库存，保证库存数量正确")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/orders/{orderId}/complete")
    public Result<Void> completeInventory(@PathVariable Long orderId) {
        boolean success = inventoryManagementService.completeInventory(orderId);
        return ResultUtils.condition(success, "完成盘点成功", "完成盘点失败");
    }

    /**
     * 取消盘点
     */
    @ApiOperation(value = "取消盘点", notes = "取消盘点单")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/orders/{orderId}/cancel")
    public Result<Void> cancelInventory(@PathVariable Long orderId) {
        boolean success = inventoryManagementService.cancelInventory(orderId);
        return ResultUtils.condition(success, "取消盘点成功", "取消盘点失败");
    }

    // ========== 盘点明细管理 ==========

    /**
     * 获取盘点明细列表
     */
    @ApiOperation(value = "获取盘点明细列表", notes = "获取盘点单的所有明细信息，包含物资信息")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/orders/{orderId}/details")
    public Result<List<InventoryDetail>> getInventoryDetails(@PathVariable Long orderId) {
        List<InventoryDetail> details = inventoryManagementService.getInventoryDetails(orderId);
        return ResultUtils.success("获取盘点明细成功", details);
    }

    /**
     * 批量更新盘点明细
     */
    @ApiOperation(value = "批量更新盘点明细", notes = "批量录入实际盘点数量")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/orders/{orderId}/details")
    public Result<Void> submitInventoryResult(@PathVariable Long orderId,
                                            @RequestBody @ApiParam("盘点明细列表") List<InventoryDetail> details) {
        boolean success = inventoryManagementService.submitInventoryResult(orderId, details);
        return ResultUtils.condition(success, "提交盘点结果成功", "提交盘点结果失败");
    }

    /**
     * 更新单个盘点明细
     */
    @ApiOperation(value = "更新盘点明细", notes = "更新单个物资的盘点结果")
    @PostMapping("/details")
    public Result<Void> updateInventoryDetail(@RequestBody @Valid @ApiParam("盘点明细") InventoryDetail detail) {
        boolean success = inventoryManagementService.updateInventoryDetail(detail);
        return ResultUtils.condition(success, "更新盘点明细成功", "更新盘点明细失败");
    }

    // ========== 盘点统计 ==========

    /**
     * 获取盘点统计信息
     */
    @ApiOperation(value = "获取盘点统计信息", notes = "获取盘点进度、差异统计等信息")
    @ApiImplicitParam(name = "orderId", value = "盘点单ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/orders/{orderId}/statistics")
    public Result<Map<String, Object>> getInventoryStatistics(@PathVariable Long orderId) {
        Map<String, Object> statistics = inventoryManagementService.getInventoryStatistics(orderId);
        return ResultUtils.success("获取盘点统计成功", statistics);
    }
}