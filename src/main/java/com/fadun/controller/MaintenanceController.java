package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.MaintenanceRecord;
import com.fadun.service.MaintenanceManagementService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 保养记录管理控制器
 * 实现保养记录的完整生命周期管理：新建记录、查询记录、保养统计、提醒管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-保养记录管理", description = "物资保养记录与维护管理")
@RestController
@RequestMapping("/api/maintenance")
@RequiredArgsConstructor
public class MaintenanceController {

    private final MaintenanceManagementService maintenanceManagementService;

    // ========== 核心功能1：新建保养记录 ==========
    
    /**
     * 新建保养记录
     */
    @ApiOperation(value = "新建保养记录", notes = "创建物资保养记录，记录保养时间、类型、负责人等信息")
    @PostMapping("/create")
    public Result<Void> createMaintenanceRecord(@RequestBody @Valid @ApiParam("保养记录") MaintenanceRecord record) {
        boolean success = maintenanceManagementService.createMaintenanceRecord(record);
        return ResultUtils.condition(success, "新建保养记录成功", "新建保养记录失败");
    }

    // ========== 核心功能2：保养记录查询 ==========
    
    /**
     * 保养记录分页查询
     */
    @ApiOperation(value = "保养记录分页查询", notes = "查询历史保养记录，支持分页和条件筛选")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "current", value = "当前页码", defaultValue = "1", dataType = "long", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "10", dataType = "long", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（物资名称/保养人）", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "materialId", value = "物资ID", dataType = "long", paramType = "query"),
            @ApiImplicitParam(name = "maintenanceType", value = "保养类型", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", dataType = "string", paramType = "query")
    })
    @GetMapping("/records")
    public Result<PageResult<MaintenanceRecord>> getMaintenanceRecords(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long materialId,
            @RequestParam(required = false) String maintenanceType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        PageResult<MaintenanceRecord> pageResult = maintenanceManagementService.getMaintenancePage(
                current, size, keyword, materialId, maintenanceType);
        return ResultUtils.success("查询保养记录成功", pageResult);
    }

    /**
     * 获取保养记录详情
     */
    @ApiOperation(value = "获取保养记录详情", notes = "根据记录ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "记录ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/records/{id}")
    public Result<MaintenanceRecord> getMaintenanceDetail(@PathVariable Long id) {
        MaintenanceRecord record = maintenanceManagementService.getMaintenanceDetail(id);
        return ResultUtils.data(record, "获取保养记录详情成功", "保养记录不存在");
    }

    /**
     * 获取物资保养历史
     */
    @ApiOperation(value = "获取物资保养历史", notes = "查看指定物资的所有保养记录")
    @ApiImplicitParam(name = "materialId", value = "物资ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/materials/{materialId}/history")
    public Result<List<MaintenanceRecord>> getMaterialMaintenanceHistory(@PathVariable Long materialId) {
        List<MaintenanceRecord> records = maintenanceManagementService.getMaterialMaintenanceHistory(materialId);
        return ResultUtils.success("获取物资保养历史成功", records);
    }

    // ========== 核心功能3：保养统计与提醒 ==========
    
    /**
     * 保养统计
     */
    @ApiOperation(value = "保养统计", notes = "获取保养工作统计信息，包括保养次数、保养类型分布等")
    @GetMapping("/statistics")
    public Result<Object> getMaintenanceStatistics() {
        // TODO: 实现保养统计功能
        return ResultUtils.success("获取保养统计成功", "统计功能待实现");
    }

    /**
     * 保养提醒列表
     */
    @ApiOperation(value = "保养提醒列表", notes = "获取需要保养的物资提醒列表，提醒到期或即将到期的保养")
    @GetMapping("/reminders")
    public Result<List<MaintenanceRecord>> getMaintenanceReminders() {
        List<MaintenanceRecord> reminders = maintenanceManagementService.getMaintenanceReminder();
        return ResultUtils.success("获取保养提醒成功", reminders);
    }

    // ========== 辅助功能：保养记录管理 ==========
    
    /**
     * 修改保养记录
     */
    @ApiOperation(value = "修改保养记录", notes = "修改已有保养记录信息")
    @ApiImplicitParam(name = "id", value = "记录ID", required = true, dataType = "long", paramType = "path")
    @PutMapping("/records/{id}")
    public Result<Void> updateMaintenanceRecord(
            @PathVariable Long id,
            @RequestBody @Valid @ApiParam("保养记录") MaintenanceRecord record) {
        
        record.setId(id);
        boolean success = maintenanceManagementService.updateMaintenanceRecord(record);
        return ResultUtils.condition(success, "修改保养记录成功", "修改保养记录失败");
    }

    /**
     * 删除保养记录
     */
    @ApiOperation(value = "删除保养记录", notes = "删除保养记录")
    @ApiImplicitParam(name = "id", value = "记录ID", required = true, dataType = "long", paramType = "path")
    @DeleteMapping("/records/{id}")
    public Result<Void> deleteMaintenanceRecord(@PathVariable Long id) {
        boolean success = maintenanceManagementService.deleteMaintenanceRecord(id);
        return ResultUtils.condition(success, "删除保养记录成功", "删除保养记录失败");
    }
}

