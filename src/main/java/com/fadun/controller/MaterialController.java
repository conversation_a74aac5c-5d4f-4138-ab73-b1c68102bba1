package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.Material;
import com.fadun.entity.MaterialCategory;
import com.fadun.entity.MaterialStock;
import com.fadun.service.MaterialManagementService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 物资台账管理控制器
 * 包含：物资信息管理 + 物资分类管理 + 库存查询功能
 * 权限：超级管理员 + 战备管理员
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/material")
@Api(tags = "战备模块-物资台账管理", description = "物资信息、分类管理和库存查询")
public class MaterialController {

    @Autowired
    private MaterialManagementService materialManagementService;

    /**
     * 权限检查：超级管理员和战备管理员可以访问
     */
    private boolean checkLogisticsPermission(HttpServletRequest request) {
        // TODO: 从session或token中获取当前登录用户信息
        // 验证用户是否为SUPER_ADMIN或LOGISTICS_ADMIN
        // SysUser currentUser = getCurrentUser(request);
        // return currentUser != null && currentUser.hasModuleAccess("logistics");
        return true;
    }

    @GetMapping("/page")
    @ApiOperation("分页查询物资")
    public Result<PageResult<Material>> getMaterialPage(
            @ApiParam("当前页") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("分类ID") @RequestParam(required = false) Long categoryId,
            @ApiParam("库室ID") @RequestParam(required = false) Long warehouseId,
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<PageResult<Material>>fail(403, "只有超级管理员和战备管理员可以访问物资管理");
        }

        PageResult<Material> result = materialManagementService.getMaterialPage(current, size, keyword, categoryId, warehouseId);
        return ResultUtils.success(result);
    }

    @PostMapping
    @ApiOperation("创建物资")
    public Result<Boolean> createMaterial(@RequestBody Material material, HttpServletRequest request) {
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员和战备管理员可以创建物资");
        }

        boolean success = materialManagementService.createMaterial(material);
        if (success) {
            log.info("创建物资成功：{}", material.getMaterialName());
            return ResultUtils.success("物资创建成功", true);
        } else {
            return ResultUtils.fail("物资创建失败");
        }
    }

    @PutMapping("/{id}")
    @ApiOperation("更新物资")
    public Result<Boolean> updateMaterial(
            @ApiParam("物资ID") @PathVariable Long id,
            @RequestBody Material material, 
            HttpServletRequest request) {
        
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员和战备管理员可以修改物资信息");
        }

        material.setId(id);
        boolean success = materialManagementService.updateMaterial(material);
        if (success) {
            log.info("更新物资成功：{}", material.getMaterialName());
            return ResultUtils.success("物资信息更新成功", true);
        } else {
            return ResultUtils.fail("物资信息更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除物资")
    public Result<Boolean> deleteMaterial(@ApiParam("物资ID") @PathVariable Long id, HttpServletRequest request) {
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Boolean>fail(403, "只有超级管理员和战备管理员可以删除物资");
        }

        boolean success = materialManagementService.deleteMaterial(id);
        if (success) {
            log.info("删除物资成功，ID：{}", id);
            return ResultUtils.success("物资删除成功", true);
        } else {
            return ResultUtils.fail("物资删除失败");
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("获取物资详情")
    public Result<Material> getMaterialDetail(@ApiParam("物资ID") @PathVariable Long id, HttpServletRequest request) {
        // 权限检查
        if (!checkLogisticsPermission(request)) {
            return ResultUtils.<Material>fail(403, "只有超级管理员和战备管理员可以查看物资详情");
        }

        Material material = materialManagementService.getMaterialDetail(id);
        return ResultUtils.success(material);
    }

    // ========== 物资分类管理功能（从MaterialCategoryController合并） ==========

    /**
     * 获取分类树形结构
     */
    @ApiOperation(value = "获取分类树形结构", notes = "获取完整的分类树形结构，用于前端树形组件")
    @GetMapping("/category/tree")
    public Result<List<MaterialCategory>> getCategoryTree() {
        List<MaterialCategory> tree = materialManagementService.getCategoryTree();
        return ResultUtils.success("获取分类树成功", tree);
    }

    /**
     * 获取分类列表
     */
    @ApiOperation(value = "获取分类列表", notes = "获取分类列表，按层级和排序排列")
    @GetMapping("/category/list")
    public Result<List<MaterialCategory>> getCategoryList() {
        List<MaterialCategory> list = materialManagementService.getCategoryList();
        return ResultUtils.success("获取分类列表成功", list);
    }

    /**
     * 获取子分类列表
     */
    @ApiOperation(value = "获取子分类列表", notes = "根据父分类ID获取子分类列表")
    @GetMapping("/category/children/{parentId}")
    public Result<List<MaterialCategory>> getChildrenCategories(@PathVariable Long parentId) {
        List<MaterialCategory> children = materialManagementService.getChildrenCategories(parentId);
        return ResultUtils.success("获取子分类成功", children);
    }

    /**
     * 获取分类详情
     */
    @ApiOperation(value = "获取分类详情", notes = "根据分类ID获取详细信息")
    @GetMapping("/category/{id}")
    public Result<MaterialCategory> getCategoryDetail(@PathVariable Long id) {
        MaterialCategory category = materialManagementService.getCategoryDetail(id);
        return ResultUtils.data(category, "获取分类详情成功", "分类不存在");
    }

    /**
     * 创建分类
     */
    @ApiOperation(value = "创建分类", notes = "新增物资分类")
    @PostMapping("/category")
    public Result<Void> createCategory(@RequestBody @Valid @ApiParam("分类信息") MaterialCategory category) {
        boolean success = materialManagementService.createCategory(category);
        return ResultUtils.condition(success, "创建分类成功", "创建分类失败");
    }

    /**
     * 更新分类
     */
    @ApiOperation(value = "更新分类", notes = "修改分类信息")
    @PutMapping("/category/{id}")
    public Result<Void> updateCategory(
            @PathVariable Long id,
            @RequestBody @Valid @ApiParam("分类信息") MaterialCategory category) {
        
        category.setId(id);
        boolean success = materialManagementService.updateCategory(category);
        return ResultUtils.condition(success, "更新分类成功", "更新分类失败");
    }

    /**
     * 删除分类
     */
    @ApiOperation(value = "删除分类", notes = "删除分类（需检查是否有子分类或物资）")
    @DeleteMapping("/category/{id}")
    public Result<Void> deleteCategory(@PathVariable Long id) {
        boolean success = materialManagementService.deleteCategory(id);
        return ResultUtils.condition(success, "删除分类成功", "删除分类失败");
    }

    /**
     * 检查分类编码是否存在
     */
    @ApiOperation(value = "检查分类编码", notes = "检查分类编码是否已存在")
    @GetMapping("/category/check-code")
    public Result<Boolean> checkCategoryCode(
            @RequestParam String categoryCode,
            @RequestParam(required = false) Long excludeId) {
        
        boolean exists = materialManagementService.existsCategoryCode(categoryCode, excludeId);
        return ResultUtils.success("检查完成", !exists);
    }

    // ========== 库存查询功能（从MaterialStockController合并） ==========

    /**
     * 获取物资总库存信息
     */
    @ApiOperation(value = "获取物资总库存", notes = "获取所有物资的总库存信息（包含所有库房的库存汇总）")
    @GetMapping("/stock/total")
    public Result<List<MaterialStock>> getMaterialWithTotalStock() {
        List<MaterialStock> stockList = materialManagementService.getMaterialWithTotalStock();
        return ResultUtils.success("查询物资总库存成功", stockList);
    }

    /**
     * 检查库存是否充足
     */
    @ApiOperation(value = "检查库存", notes = "检查指定物资在指定库房的库存是否充足")
    @GetMapping("/stock/check")
    public Result<Boolean> checkStockSufficient(
            @RequestParam Long materialId,
            @RequestParam Long warehouseId,
            @RequestParam Integer quantity) {
        boolean sufficient = materialManagementService.checkStockSufficient(materialId, warehouseId, quantity);
        return ResultUtils.success("检查库存完成", sufficient);
    }

    /**
     * 分页查询库存信息
     */
    @ApiOperation(value = "分页查询库存", notes = "获取库存信息列表，支持关键词搜索和条件筛选")
    @GetMapping("/stock/page")
    public Result<PageResult<MaterialStock>> getStockPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long materialId,
            @RequestParam(required = false) Long warehouseId,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String stockStatus) {
        
        PageResult<MaterialStock> result = materialManagementService.getStockPage(current, size, keyword, materialId, warehouseId, categoryId, stockStatus);
        return ResultUtils.success("查询库存信息成功", result);
    }

    // ========== 资源实力统计功能（新增） ==========

    /**
     * 物资统计概览
     */
    @ApiOperation(value = "物资统计概览", notes = "物资种类、分类分布、状态统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getMaterialStatistics() {
        Map<String, Object> statistics = materialManagementService.getMaterialStatistics();
        return ResultUtils.success("获取物资统计成功", statistics);
    }

    /**
     * 资源实力分析
     */
    @ApiOperation(value = "资源实力分析", notes = "物资配置合理性分析、资源储备充足度评估")
    @GetMapping("/resource-analysis")
    public Result<Map<String, Object>> getResourceAnalysis() {
        Map<String, Object> analysis = materialManagementService.getResourceAnalysis();
        return ResultUtils.success("获取资源实力分析成功", analysis);
    }

    /**
     * 物资台账导出
     */
    @ApiOperation(value = "物资台账导出", notes = "导出完整物资台账，支持Excel格式")
    @GetMapping("/export")
    public Result<Object> exportMaterial(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long warehouseId) {
        Object exportData = materialManagementService.exportMaterial(keyword, categoryId, warehouseId);
        return ResultUtils.success("导出功能成功", exportData);
    }

    // ========== 基础数据查询接口（供入库/出库使用） ==========

    /**
     * 根据库室查询物资列表
     * 供入库、出库等模块调用的基础数据接口
     */
    @ApiOperation(value = "根据库室查询物资", notes = "根据库室ID获取该库室下的所有物资信息，包含库存信息")
    @GetMapping("/by-warehouse/{warehouseId}")
    public Result<List<Material>> getMaterialsByWarehouse(@PathVariable Long warehouseId) {
        List<Material> materials = materialManagementService.getMaterialsByWarehouse(warehouseId);
        return ResultUtils.success("查询库室物资成功", materials);
    }

}
