package com.fadun.controller;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.OutboundOrder;
import com.fadun.entity.OutboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;
import com.fadun.service.OutboundManagementService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 物资出库管理控制器
 * 实现2个核心功能：提交出库单、出库记录查询
 * 注意：库室列表请使用 GET /api/warehouse/list
 *      库室物资请使用 GET /api/material/by-warehouse/{warehouseId}
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-物资出库管理", description = "物资借出登记功能")
@RestController
@RequestMapping("/api/outbound")
@RequiredArgsConstructor
public class OutboundController {

    private final OutboundManagementService outboundManagementService;

    // ========== 核心功能：提交出库单 ==========
    
    /**
     * 提交出库单
     */
    @ApiOperation(value = "提交出库单", notes = "执行物资借出出库操作")
    @PostMapping("/submit")
    public Result<String> submitOutboundOrder(@RequestBody @Valid @ApiParam("出库单信息") OutboundOrder outboundOrder) {
        return outboundManagementService.submitOutboundOrder(outboundOrder);
    }

    // ========== 核心功能：出库记录分页与条件查询 ==========
    
    /**
     * 出库记录分页与条件查询
     */
    @ApiOperation(value = "出库记录查询", notes = "查询历史出库记录，支持分页和条件筛选")
    @GetMapping("/records")
    public Result<PageResult<OutboundOrder>> getOutboundRecords(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long warehouseId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        // 使用现有的分页查询方法，忽略日期参数
        PageResult<OutboundOrder> pageResult = outboundManagementService.getOutboundRecords(current, size, keyword, warehouseId, startDate, endDate);
        return ResultUtils.success("查询出库记录成功", pageResult);
    }

    // ========== 出库单明细管理（从OutboundDetailController合并） ==========
    
    /**
     * 根据出库单ID查询明细列表
     */
    @ApiOperation(value = "查询出库明细", notes = "根据出库单ID查询明细列表")
    @GetMapping("/orders/{orderId}/details")
    public Result<List<OutboundDetail>> getDetailsByOrderId(@PathVariable Long orderId) {
        List<OutboundDetail> details = outboundManagementService.getDetailsByOrderId(orderId);
        return ResultUtils.success("查询出库明细成功", details);
    }

    /**
     * 批量保存出库明细
     */
    @ApiOperation(value = "批量保存明细", notes = "批量保存出库明细")
    @PostMapping("/details/batch")
    public Result<Void> saveDetails(@RequestBody @ApiParam("明细列表") List<OutboundDetail> details) {
        boolean success = outboundManagementService.saveDetails(details);
        return ResultUtils.condition(success, "保存明细成功", "保存明细失败");
    }
}
