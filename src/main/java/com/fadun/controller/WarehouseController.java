package com.fadun.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.Warehouse;
import com.fadun.service.WarehouseManagementService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 库室信息管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Api(tags = "战备模块-库室管理", description = "库室信息管理")
@RestController
@RequestMapping("/api/warehouse")
@RequiredArgsConstructor
public class WarehouseController {

    private final WarehouseManagementService warehouseManagementService;

    /**
     * 分页查询库室信息
     */
    @ApiOperation(value = "分页查询库室信息", notes = "支持关键词搜索和状态筛选")
    @GetMapping("/page")
    public Result<IPage<Warehouse>> getWarehousePage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        
        Page<Warehouse> page = new Page<>(current, size);
        IPage<Warehouse> result = warehouseManagementService.getWarehousePage(page, keyword, status);
        return ResultUtils.success("查询成功", result);
    }

    /**
     * 获取库室列表
     */
    @ApiOperation(value = "获取库室列表", notes = "获取所有启用状态的库室列表")
    @GetMapping("/list")
    public Result<List<Warehouse>> getWarehouseList() {
        List<Warehouse> list = warehouseManagementService.getWarehouseList();
        return ResultUtils.success("获取库室列表成功", list);
    }

    /**
     * 获取库室详情
     */
    @ApiOperation(value = "获取库室详情", notes = "根据库室ID获取详细信息")
    @GetMapping("/{id}")
    public Result<Warehouse> getWarehouseDetail(@PathVariable Long id) {
        Warehouse warehouse = warehouseManagementService.getWarehouseDetail(id);
        return ResultUtils.data(warehouse, "获取库室详情成功", "库室不存在");
    }

    /**
     * 创建库室
     */
    @ApiOperation(value = "创建库室", notes = "新增库室信息")
    @PostMapping("/create")
    public Result<Void> createWarehouse(@RequestBody @Valid @ApiParam("库室信息") Warehouse warehouse) {
        boolean success = warehouseManagementService.createWarehouse(warehouse);
        return ResultUtils.condition(success, "创建库室成功", "创建库室失败");
    }

    /**
     * 更新库室
     */
    @ApiOperation(value = "更新库室", notes = "修改库室信息")
    @PutMapping("/{id}")
    public Result<Void> updateWarehouse(
            @PathVariable Long id,
            @RequestBody @Valid @ApiParam("库室信息") Warehouse warehouse) {
        
        warehouse.setId(id);
        boolean success = warehouseManagementService.updateWarehouse(warehouse);
        return ResultUtils.condition(success, "更新库室成功", "更新库室失败");
    }

    /**
     * 删除库室
     */
    @ApiOperation(value = "删除库室", notes = "删除库室（需检查是否有关联物资）")
    @DeleteMapping("/{id}")
    public Result<Void> deleteWarehouse(@PathVariable Long id) {
        boolean success = warehouseManagementService.deleteWarehouse(id);
        return ResultUtils.condition(success, "删除库室成功", "删除库室失败");
    }

    /**
     * 检查库室编码是否存在
     */
    @ApiOperation(value = "检查库室编码", notes = "检查库室编码是否已存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkWarehouseCode(
            @RequestParam String warehouseCode,
            @RequestParam(required = false) Long excludeId) {
        
        boolean exists = warehouseManagementService.existsWarehouseCode(warehouseCode, excludeId);
        return ResultUtils.success("检查完成", !exists);
    }

    /**
     * 获取库室及其物资数量
     */
    @ApiOperation(value = "获取库室统计", notes = "获取库室及其物资数量统计")
    @GetMapping("/statistics")
    public Result<List<Warehouse>> getWarehouseStatistics() {
        List<Warehouse> list = warehouseManagementService.getWarehouseWithMaterialCount();
        return ResultUtils.success("获取库室统计成功", list);
    }
}
