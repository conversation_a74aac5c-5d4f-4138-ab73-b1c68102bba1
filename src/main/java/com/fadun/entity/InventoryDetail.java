package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 盘点明细实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inventory_detail")
@ApiModel(description = "盘点明细")
public class InventoryDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "盘点单ID")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "物资ID")
    @TableField("material_id")
    private Long materialId;

    @ApiModelProperty(value = "账面数量")
    @TableField("book_quantity")
    private Integer bookQuantity;

    @ApiModelProperty(value = "实际数量")
    @TableField("actual_quantity")
    private Integer actualQuantity;

    @ApiModelProperty(value = "差异数量")
    @TableField("difference_quantity")
    private Integer differenceQuantity;

    @ApiModelProperty(value = "存放位置")
    @TableField("location")
    private String location;

    @ApiModelProperty(value = "状态：1-待盘点，2-已盘点")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "物资编码")
    @TableField(exist = false)
    private String materialCode;

    @ApiModelProperty(value = "物资名称")
    @TableField(exist = false)
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    @TableField(exist = false)
    private String specification;

    @ApiModelProperty(value = "计量单位")
    @TableField(exist = false)
    private String unit;

    @ApiModelProperty(value = "分类名称")
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "状态名称")
    @TableField(exist = false)
    private String statusName;

    @ApiModelProperty(value = "差异类型：盈余、亏损、正常")
    @TableField(exist = false)
    private String differenceType;

    // ========== 状态常量 ==========

    /**
     * 盘点明细状态常量
     */
    public static class Status {
        /** 待盘点 */
        public static final Integer PENDING = 1;
        /** 已盘点 */
        public static final Integer COMPLETED = 2;
    }

    // ========== 业务方法 ==========

    /**
     * 计算差异数量
     */
    public void calculateDifference() {
        if (actualQuantity != null && bookQuantity != null) {
            this.differenceQuantity = actualQuantity - bookQuantity;
        }
    }

    /**
     * 获取差异类型
     */
    public String getDifferenceType() {
        if (differenceQuantity == null) {
            return "未盘点";
        }
        if (differenceQuantity > 0) {
            return "盈余";
        } else if (differenceQuantity < 0) {
            return "亏损";
        } else {
            return "正常";
        }
    }

    /**
     * 是否有差异
     */
    public boolean hasDifference() {
        return differenceQuantity != null && differenceQuantity != 0;
    }
}
