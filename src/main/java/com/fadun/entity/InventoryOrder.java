package com.fadun.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘点单实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("inventory_order")
@ApiModel(description = "盘点单")
public class InventoryOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "盘点单号")
    @TableField("order_no")
    private String orderNo;

    @ApiModelProperty(value = "库室ID")
    @TableField("warehouse_id")
    private Long warehouseId;

    @ApiModelProperty(value = "盘点类型：full-全盘，partial-抽盘")
    @TableField("inventory_type")
    private String inventoryType;

    @ApiModelProperty(value = "盘点日期")
    @TableField("inventory_date")
    private LocalDate inventoryDate;

    @ApiModelProperty(value = "盘点员ID")
    @TableField("operator_id")
    private Long operatorId;

    @ApiModelProperty(value = "盘点员姓名")
    @TableField("operator_name")
    private String operatorName;

    @ApiModelProperty(value = "状态：1-盘点中，2-已完成，3-已取消")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "盘点物资种类数")
    @TableField("total_items")
    private Integer totalItems;

    @ApiModelProperty(value = "差异物资种类数")
    @TableField("diff_items")
    private Integer diffItems;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    // ========== 扩展字段 ==========

    @ApiModelProperty(value = "库室名称")
    @TableField(exist = false)
    private String warehouseName;

    @ApiModelProperty(value = "盘点明细列表")
    @TableField(exist = false)
    private List<InventoryDetail> details;

    @ApiModelProperty(value = "状态名称")
    @TableField(exist = false)
    private String statusName;

    @ApiModelProperty(value = "盘点类型名称")
    @TableField(exist = false)
    private String inventoryTypeName;

    // ========== 状态常量 ==========

    /**
     * 盘点状态常量
     */
    public static class Status {
        /** 盘点中 */
        public static final Integer IN_PROGRESS = 1;
        /** 已完成 */
        public static final Integer COMPLETED = 2;
        /** 已取消 */
        public static final Integer CANCELLED = 3;
    }

    /**
     * 盘点类型常量
     */
    public static class InventoryType {
        /** 全盘 */
        public static final String FULL = "full";
        /** 抽盘 */
        public static final String PARTIAL = "partial";
    }
}
