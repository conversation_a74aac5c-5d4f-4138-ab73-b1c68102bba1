package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.InboundOrder;
import com.fadun.entity.InboundDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 入库管理Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface InboundMapper extends BaseMapper<InboundOrder> {

    /**
     * 分页查询入库单
     */
    Page<InboundOrder> selectInboundPage(Page<InboundOrder> page,
                                        @Param("keyword") String keyword,
                                        @Param("inboundType") String inboundType,
                                        @Param("status") Integer status);

    // ==================== 入库单明细相关方法 ====================

    /**
     * 根据入库单ID查询明细列表（包含物资信息）
     */
    @Select("SELECT d.*, m.material_code, m.material_name, m.specification, m.unit " +
            "FROM inbound_detail d " +
            "LEFT JOIN material m ON d.material_id = m.id " +
            "WHERE d.order_id = #{orderId}")
    List<InboundDetail> selectDetailsByOrderId(@Param("orderId") Long orderId);

    /**
     * 批量保存入库明细
     */
    int insertBatch(@Param("details") List<InboundDetail> details);

    // ==================== 入库记录查询相关方法 ====================

    /**
     * 根据条件分页查询入库记录（支持日期范围）
     */
    Page<InboundOrder> selectInboundRecords(Page<InboundOrder> page,
                                           @Param("keyword") String keyword,
                                           @Param("warehouseId") Long warehouseId,
                                           @Param("startDate") String startDate,
                                           @Param("endDate") String endDate);

    /**
     * 根据ID查询入库单详情（包含库房信息）
     */
    InboundOrder selectInboundDetailById(@Param("id") Long id);

    // ==================== 统计分析相关方法 ====================

    /**
     * 统计入库概览信息
     */
    java.util.Map<String, Object> getInboundStatistics();

    /**
     * 按月统计入库数量
     */
    List<java.util.Map<String, Object>> getMonthlyInboundStats();

    /**
     * 按入库类型统计
     */
    List<java.util.Map<String, Object>> getInboundTypeStats();
}
