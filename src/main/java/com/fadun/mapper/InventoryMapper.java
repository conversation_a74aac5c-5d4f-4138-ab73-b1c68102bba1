package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.MaterialStock;
import com.fadun.entity.InventoryOrder;
import com.fadun.entity.InventoryDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 库存盘点Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface InventoryMapper extends BaseMapper<InventoryOrder> {

    // ==================== 盘点单管理 ====================

    /**
     * 分页查询盘点单
     */
    Page<InventoryOrder> selectInventoryOrderPage(Page<InventoryOrder> page,
                                                 @Param("keyword") String keyword,
                                                 @Param("warehouseId") Long warehouseId,
                                                 @Param("inventoryType") String inventoryType,
                                                 @Param("status") Integer status);

    /**
     * 根据ID查询盘点单详情（包含库室信息）
     */
    @Select("SELECT io.*, w.warehouse_name " +
            "FROM inventory_order io " +
            "LEFT JOIN warehouse w ON io.warehouse_id = w.id " +
            "WHERE io.id = #{orderId}")
    InventoryOrder selectInventoryOrderDetail(@Param("orderId") Long orderId);

    // ==================== 盘点明细管理 ====================

    /**
     * 根据盘点单ID查询明细列表（包含物资信息）
     */
    @Select("SELECT id.*, m.material_code, m.material_name, m.specification, " +
            "m.unit, c.category_name " +
            "FROM inventory_detail id " +
            "LEFT JOIN material m ON id.material_id = m.id " +
            "LEFT JOIN material_category c ON m.category_id = c.id " +
            "WHERE id.order_id = #{orderId} " +
            "ORDER BY m.material_code")
    List<InventoryDetail> selectInventoryDetailsByOrderId(@Param("orderId") Long orderId);

    /**
     * 批量插入盘点明细
     */
    int insertInventoryDetailBatch(@Param("details") List<InventoryDetail> details);

    /**
     * 更新盘点明细
     */
    int updateInventoryDetail(InventoryDetail detail);

    /**
     * 根据盘点单ID和物资ID查询库存信息
     */
    @Select("SELECT ms.* FROM material_stock ms " +
            "INNER JOIN inventory_order io ON ms.warehouse_id = io.warehouse_id " +
            "WHERE io.id = #{orderId} AND ms.material_id IN " +
            "<foreach collection='materialIds' item='materialId' open='(' separator=',' close=')'>" +
            "#{materialId}" +
            "</foreach>")
    List<MaterialStock> selectStockByOrderIdAndMaterialIds(@Param("orderId") Long orderId, 
                                                           @Param("materialIds") List<Long> materialIds);

    // ==================== 库存查询功能 ====================

    /**
     * 分页查询库存信息
     */
    Page<MaterialStock> selectStockPage(Page<MaterialStock> page,
                                        @Param("keyword") String keyword,
                                        @Param("materialId") Long materialId,
                                        @Param("warehouseId") Long warehouseId,
                                        @Param("categoryId") Long categoryId,
                                        @Param("stockStatus") String stockStatus);

    /**
     * 获取库存统计信息
     */
    List<MaterialStock> selectStockStatistics(@Param("warehouseId") Long warehouseId);

    /**
     * 更新库存数量
     */
    int updateStock(@Param("materialId") Long materialId,
                    @Param("warehouseId") Long warehouseId,
                    @Param("quantity") Integer quantity,
                    @Param("type") String type);

    /**
     * 增加库存
     */
    @Update("UPDATE material_stock SET current_stock = current_stock + #{quantity}, " +
            "last_in_time = NOW(), update_time = NOW() " +
            "WHERE material_id = #{materialId} AND warehouse_id = #{warehouseId}")
    int increaseStock(@Param("materialId") Long materialId,
                     @Param("warehouseId") Long warehouseId,
                     @Param("quantity") Integer quantity);

    /**
     * 减少库存
     */
    @Update("UPDATE material_stock SET current_stock = current_stock - #{quantity}, " +
            "last_out_time = NOW(), update_time = NOW() " +
            "WHERE material_id = #{materialId} AND warehouse_id = #{warehouseId} " +
            "AND current_stock >= #{quantity}")
    int decreaseStock(@Param("materialId") Long materialId,
                     @Param("warehouseId") Long warehouseId,
                     @Param("quantity") Integer quantity);

    /**
     * 查询物资总库存（所有库房的库存之和）
     */
    @Select("SELECT m.id as materialId, m.material_code as materialCode, " +
            "m.material_name as materialName, m.specification, m.unit, " +
            "m.category_id as categoryId, c.category_name as categoryName, " +
            "COALESCE(SUM(s.current_stock), 0) as totalStock " +
            "FROM material m " +
            "LEFT JOIN material_stock s ON m.id = s.material_id " +
            "LEFT JOIN material_category c ON m.category_id = c.id " +
            "WHERE m.status = 1 " +
            "GROUP BY m.id, m.material_code, m.material_name, m.specification, m.unit, m.category_id, c.category_name " +
            "ORDER BY m.create_time DESC")
    List<MaterialStock> selectMaterialWithTotalStock();
}
