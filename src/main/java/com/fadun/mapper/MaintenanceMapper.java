package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.MaintenanceRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 保养管理Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface MaintenanceMapper extends BaseMapper<MaintenanceRecord> {

    /**
     * 分页查询保养记录
     */
    Page<MaintenanceRecord> selectMaintenancePage(Page<MaintenanceRecord> page,
                                                 @Param("keyword") String keyword,
                                                 @Param("materialId") Long materialId,
                                                 @Param("maintenanceType") String maintenanceType);

    /**
     * 根据物资ID查询保养历史
     */
    @Select("SELECT mr.*, m.material_name, m.material_code " +
            "FROM maintenance_record mr " +
            "LEFT JOIN material m ON mr.material_id = m.id " +
            "WHERE mr.material_id = #{materialId} " +
            "ORDER BY mr.maintenance_date DESC")
    List<MaintenanceRecord> selectMaintenanceHistoryByMaterialId(@Param("materialId") Long materialId);

    /**
     * 查询保养提醒（需要保养的物资）
     */
    @Select("SELECT mr.*, m.material_name, m.material_code " +
            "FROM maintenance_record mr " +
            "LEFT JOIN material m ON mr.material_id = m.id " +
            "WHERE mr.next_maintenance_date <= CURDATE() " +
            "AND mr.status = 1 " +
            "ORDER BY mr.next_maintenance_date ASC")
    List<MaintenanceRecord> selectMaintenanceReminder();

}
