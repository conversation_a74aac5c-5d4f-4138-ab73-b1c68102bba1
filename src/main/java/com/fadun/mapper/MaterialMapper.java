package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.Material;
import com.fadun.entity.MaterialCategory;
import com.fadun.entity.MaterialStock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 物资台账Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface MaterialMapper extends BaseMapper<Material> {

    /**
     * 分页查询物资台账
     */
    Page<Material> selectMaterialPage(Page<Material> page, 
                                      @Param("keyword") String keyword,
                                      @Param("categoryId") Long categoryId,
                                      @Param("warehouseId") Long warehouseId);

    /**
     * 根据库室ID查询物资列表
     */
    List<Material> selectMaterialsByWarehouse(@Param("warehouseId") Long warehouseId);

    /**
     * 查询物资详情（包含分类信息和库存汇总）
     */
    Material selectMaterialDetailById(@Param("id") Long id);

    /**
     * 检查物资编码是否存在
     */
    int countByMaterialCode(@Param("materialCode") String materialCode, @Param("excludeId") Long excludeId);

    /**
     * 统计物资总数量
     */
    int countTotalMaterials();

    /**
     * 统计各分类物资数量
     */
    List<Map<String, Object>> countMaterialsByCategory();

    /**
     * 统计各状态物资数量
     */
    List<Map<String, Object>> countMaterialsByStatus();

    /**
     * 获取物资统计概览
     */
    Map<String, Object> getMaterialStatisticsOverview();

    /**
     * 查询库存不足的物资
     */
    List<Map<String, Object>> selectLowStockMaterials();

    // ==================== 物资分类相关方法 ====================

    /**
     * 查询分类树形结构
     */
    List<MaterialCategory> selectCategoryTree(@Param("parentId") Long parentId);

    /**
     * 查询分类及其物资数量
     */
    List<MaterialCategory> selectCategoryWithMaterialCount();

    /**
     * 检查分类下是否有物资
     */
    int countMaterialsInCategory(@Param("categoryId") Long categoryId);

    /**
     * 检查分类下是否有子分类
     */
    int countChildrenByCategory(@Param("categoryId") Long categoryId);

    /**
     * 检查分类编码是否存在
     */
    int countByCategoryCode(@Param("categoryCode") String categoryCode, @Param("excludeId") Long excludeId);

    /**
     * 根据ID查询分类详情
     */
    MaterialCategory selectCategoryById(@Param("id") Long id);

    /**
     * 查询所有分类列表
     */
    List<MaterialCategory> selectAllCategories();

    /**
     * 根据父级ID查询子分类
     */
    List<MaterialCategory> selectCategoriesByParentId(@Param("parentId") Long parentId);

    /**
     * 插入分类
     */
    int insertCategory(MaterialCategory category);

    /**
     * 更新分类
     */
    int updateCategoryById(MaterialCategory category);

    /**
     * 删除分类
     */
    int deleteCategoryById(@Param("id") Long id);

    // ==================== 库存查询相关方法 ====================

    /**
     * 分页查询库存信息
     */
    Page<MaterialStock> selectStockPage(Page<MaterialStock> page,
                                        @Param("keyword") String keyword,
                                        @Param("materialId") Long materialId,
                                        @Param("warehouseId") Long warehouseId,
                                        @Param("categoryId") Long categoryId,
                                        @Param("stockStatus") String stockStatus);

    /**
     * 查询物资的总库存信息
     */
    List<MaterialStock> selectMaterialWithTotalStock(@Param("keyword") String keyword,
                                                     @Param("categoryId") Long categoryId);

    /**
     * 检查指定物资在指定库房的库存数量
     */
    MaterialStock selectStockByMaterialAndWarehouse(@Param("materialId") Long materialId,
                                                    @Param("warehouseId") Long warehouseId);

    /**
     * 统计库存概览信息
     */
    Map<String, Object> getStockOverview(@Param("warehouseId") Long warehouseId);

    /**
     * 查询库存预警信息
     */
    List<Map<String, Object>> selectStockWarnings(@Param("lowStockThreshold") Integer lowStockThreshold);

    /**
     * 根据库房ID统计各状态库存数量
     */
    List<Map<String, Object>> countStockByStatus(@Param("warehouseId") Long warehouseId);
}
