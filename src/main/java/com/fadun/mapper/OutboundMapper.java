package com.fadun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.OutboundOrder;
import com.fadun.entity.OutboundDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 出库管理Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface OutboundMapper extends BaseMapper<OutboundOrder> {

    /**
     * 分页查询出库单
     */
    Page<OutboundOrder> selectOutboundPage(Page<OutboundOrder> page,
                                          @Param("keyword") String keyword,
                                          @Param("outboundType") String outboundType,
                                          @Param("status") Integer status);

    // ==================== 出库单明细相关方法 ====================

    /**
     * 根据出库单ID查询明细列表（包含物资信息）
     */
    @Select("SELECT d.*, m.material_code, m.material_name, m.specification, m.unit " +
            "FROM outbound_detail d " +
            "LEFT JOIN material m ON d.material_id = m.id " +
            "WHERE d.order_id = #{orderId}")
    List<OutboundDetail> selectDetailsByOrderId(@Param("orderId") Long orderId);

    /**
     * 批量保存出库明细
     */
    int insertBatch(@Param("details") List<OutboundDetail> details);

    // ==================== 出库记录查询相关方法 ====================

    /**
     * 根据条件分页查询出库记录（支持日期范围）
     */
    Page<OutboundOrder> selectOutboundRecords(Page<OutboundOrder> page,
                                             @Param("keyword") String keyword,
                                             @Param("warehouseId") Long warehouseId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);

    /**
     * 根据ID查询出库单详情（包含库房信息）
     */
    OutboundOrder selectOutboundDetailById(@Param("id") Long id);

    // ==================== 统计分析相关方法 ====================

    /**
     * 统计出库概览信息
     */
    java.util.Map<String, Object> getOutboundStatistics();

    /**
     * 按月统计出库数量
     */
    List<java.util.Map<String, Object>> getMonthlyOutboundStats();

    /**
     * 按出库类型统计
     */
    List<java.util.Map<String, Object>> getOutboundTypeStats();
}
