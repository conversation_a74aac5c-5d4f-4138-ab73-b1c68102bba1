package com.fadun.service;

import com.fadun.common.result.PageResult;
import com.fadun.entity.InboundOrder;
import com.fadun.entity.InboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;

import java.util.List;

/**
 * 物资入库管理服务接口
 * 整合入库流程和明细管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InboundManagementService {

    // ========== 核心入库功能 ==========

    /**
     * 获取库室列表
     */
    List<Warehouse> getWarehouseList();

    /**
     * 根据库室ID获取物资列表
     */
    List<Material> getMaterialsByWarehouse(Long warehouseId);

    /**
     * 提交入库单
     */
    boolean submitInboundOrder(InboundOrder inboundOrder);

    /**
     * 入库记录分页查询
     */
    PageResult<InboundOrder> getInboundRecords(Long current, Long size, String keyword, 
                                              Long warehouseId, String startDate, String endDate);

    // ========== 入库单明细管理 ==========

    /**
     * 根据入库单ID查询明细列表
     */
    List<InboundDetail> getDetailsByOrderId(Long orderId);

    /**
     * 批量保存入库明细
     */
    boolean saveDetails(List<InboundDetail> details);

    // ========== 传统入库管理功能（保留兼容） ==========

    /**
     * 分页查询入库单
     */
    PageResult<InboundOrder> getInboundPage(Long current, Long size, String keyword, String inboundType, Integer status);

    /**
     * 创建入库单
     */
    boolean createInboundOrder(InboundOrder inboundOrder);

    /**
     * 确认入库
     */
    boolean confirmInbound(Long orderId);

    /**
     * 取消入库
     */
    boolean cancelInbound(Long orderId);

    /**
     * 获取入库单详情
     */
    InboundOrder getInboundDetail(Long orderId);
}

