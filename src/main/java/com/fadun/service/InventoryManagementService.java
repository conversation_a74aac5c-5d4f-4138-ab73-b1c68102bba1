package com.fadun.service;

import com.fadun.common.result.PageResult;
import com.fadun.entity.MaterialStock;
import com.fadun.entity.InventoryOrder;
import com.fadun.entity.InventoryDetail;

import java.util.List;
import java.util.Map;

/**
 * 库存盘点管理服务接口
 * 专注于库存盘点管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface InventoryManagementService {

    // ========== 盘点专用库存统计功能 ==========

    /**
     * 获取库存统计信息（专用于盘点分析）
     */
    Map<String, Object> getStockStatistics(Long warehouseId);

    // ========== 库存盘点管理功能 ==========

    /**
     * 分页查询盘点单
     */
    PageResult<InventoryOrder> getInventoryOrderPage(Long current, Long size, String keyword, 
                                                    Long warehouseId, String inventoryType, Integer status);

    /**
     * 创建盘点单
     */
    boolean createInventoryOrder(InventoryOrder inventoryOrder);

    /**
     * 获取盘点单详情
     */
    InventoryOrder getInventoryOrderDetail(Long orderId);

    /**
     * 获取盘点明细列表
     */
    List<InventoryDetail> getInventoryDetails(Long orderId);

    /**
     * 批量保存盘点明细
     */
    boolean saveInventoryDetails(List<InventoryDetail> details);

    /**
     * 更新盘点明细
     */
    boolean updateInventoryDetail(InventoryDetail detail);

    /**
     * 开始盘点（生成盘点明细）
     */
    boolean startInventory(Long orderId, List<Long> materialIds);

    /**
     * 提交盘点结果
     */
    boolean submitInventoryResult(Long orderId, List<InventoryDetail> details);

    /**
     * 完成盘点
     */
    boolean completeInventory(Long orderId);

    /**
     * 取消盘点
     */
    boolean cancelInventory(Long orderId);

    /**
     * 获取盘点统计信息
     */
    Map<String, Object> getInventoryStatistics(Long orderId);

    // ========== 库存操作功能 ==========

    /**
     * 更新库存
     */
    boolean updateStock(Long materialId, Long warehouseId, Integer quantity, String type);
}
