package com.fadun.service;

import com.fadun.common.result.PageResult;
import com.fadun.entity.MaintenanceRecord;

import java.util.List;

/**
 * 保养记录管理服务接口
 * 专注于保养记录管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MaintenanceManagementService {

    /**
     * 分页查询保养记录
     */
    PageResult<MaintenanceRecord> getMaintenancePage(Long current, Long size, String keyword, Long materialId, String maintenanceType);

    /**
     * 创建保养记录
     */
    boolean createMaintenanceRecord(MaintenanceRecord record);

    /**
     * 获取保养记录详情
     */
    MaintenanceRecord getMaintenanceDetail(Long id);

    /**
     * 更新保养记录
     */
    boolean updateMaintenanceRecord(MaintenanceRecord record);

    /**
     * 删除保养记录
     */
    boolean deleteMaintenanceRecord(Long id);

    /**
     * 获取物资保养历史
     */
    List<MaintenanceRecord> getMaterialMaintenanceHistory(Long materialId);

    /**
     * 获取保养提醒
     */
    List<MaintenanceRecord> getMaintenanceReminder();
}

