package com.fadun.service;

import com.fadun.common.result.PageResult;
import com.fadun.entity.Material;
import com.fadun.entity.MaterialCategory;
import com.fadun.entity.MaterialStock;

import java.util.List;
import java.util.Map;

/**
 * 物资台账管理服务接口
 * 整合物资信息、分类管理、库存查询功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MaterialManagementService {

    // ========== 物资信息管理 ==========

    /**
     * 分页查询物资台账
     */
    PageResult<Material> getMaterialPage(Long current, Long size, String keyword, Long categoryId, Long warehouseId);

    /**
     * 创建物资信息
     */
    boolean createMaterial(Material material);

    /**
     * 更新物资信息
     */
    boolean updateMaterial(Material material);

    /**
     * 删除物资信息
     */
    boolean deleteMaterial(Long id);

    /**
     * 获取物资详情
     */
    Material getMaterialDetail(Long id);

    /**
     * 根据库室ID获取物资列表
     */
    List<Material> getMaterialsByWarehouse(Long warehouseId);

    // ========== 物资分类管理 ==========

    /**
     * 获取分类树形结构
     */
    List<MaterialCategory> getCategoryTree();

    /**
     * 获取分类列表
     */
    List<MaterialCategory> getCategoryList();

    /**
     * 获取子分类列表
     */
    List<MaterialCategory> getChildrenCategories(Long parentId);

    /**
     * 获取分类详情
     */
    MaterialCategory getCategoryDetail(Long id);

    /**
     * 创建分类
     */
    boolean createCategory(MaterialCategory category);

    /**
     * 更新分类
     */
    boolean updateCategory(MaterialCategory category);

    /**
     * 删除分类
     */
    boolean deleteCategory(Long id);

    /**
     * 检查分类编码是否存在
     */
    boolean existsCategoryCode(String categoryCode, Long excludeId);

    // ========== 库存查询功能 ==========

    /**
     * 获取物资总库存信息
     */
    List<MaterialStock> getMaterialWithTotalStock();

    /**
     * 检查库存是否充足
     */
    boolean checkStockSufficient(Long materialId, Long warehouseId, Integer quantity);

    /**
     * 分页查询库存信息
     */
    PageResult<MaterialStock> getStockPage(Long current, Long size, String keyword, 
                                          Long materialId, Long warehouseId, Long categoryId, String stockStatus);

    // ========== 统计分析功能 ==========

    /**
     * 获取物资统计信息
     */
    Map<String, Object> getMaterialStatistics();

    /**
     * 获取资源分析数据
     */
    Map<String, Object> getResourceAnalysis();

    /**
     * 导出物资数据
     */
    Object exportMaterial(String keyword, Long categoryId, Long warehouseId);
}
