package com.fadun.service;

import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.entity.OutboundOrder;
import com.fadun.entity.OutboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;

import java.util.List;

/**
 * 物资出库管理服务接口
 * 整合出库流程和明细管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface OutboundManagementService {

    // ========== 核心出库功能 ==========

    /**
     * 获取库室列表
     */
    List<Warehouse> getWarehouseList();

    /**
     * 根据库室ID获取物资列表
     */
    List<Material> getMaterialsByWarehouse(Long warehouseId);

    /**
     * 提交出库单
     */
    Result<String> submitOutboundOrder(OutboundOrder outboundOrder);

    /**
     * 出库记录分页查询
     */
    PageResult<OutboundOrder> getOutboundRecords(Long current, Long size, String keyword, 
                                                Long warehouseId, String startDate, String endDate);

    // ========== 出库单明细管理 ==========

    /**
     * 根据出库单ID查询明细列表
     */
    List<OutboundDetail> getDetailsByOrderId(Long orderId);

    /**
     * 批量保存出库明细
     */
    boolean saveDetails(List<OutboundDetail> details);

    // ========== 传统出库管理功能（保留兼容） ==========

    /**
     * 分页查询出库单
     */
    PageResult<OutboundOrder> getOutboundPage(Long current, Long size, String keyword, String outboundType, Integer status);

    /**
     * 创建出库单
     */
    Result<String> createOutboundOrder(OutboundOrder outboundOrder);

    /**
     * 确认出库
     */
    boolean confirmOutbound(Long orderId);

    /**
     * 取消出库
     */
    boolean cancelOutbound(Long orderId);

    /**
     * 获取出库单详情
     */
    OutboundOrder getOutboundDetail(Long orderId);
}

