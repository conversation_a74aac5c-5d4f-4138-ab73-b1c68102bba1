package com.fadun.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.entity.Warehouse;

import java.util.List;
import java.util.Map;

/**
 * 库室管理服务接口
 * 专注于库室管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WarehouseManagementService {

    /**
     * 分页查询库室信息
     */
    IPage<Warehouse> getWarehousePage(Page<Warehouse> page, String keyword, Integer status);

    /**
     * 获取库室列表
     */
    List<Warehouse> getWarehouseList();

    /**
     * 获取库室详情
     */
    Warehouse getWarehouseDetail(Long id);

    /**
     * 创建库室
     */
    boolean createWarehouse(Warehouse warehouse);

    /**
     * 更新库室
     */
    boolean updateWarehouse(Warehouse warehouse);

    /**
     * 删除库室
     */
    boolean deleteWarehouse(Long id);

    /**
     * 检查库室编码是否存在
     */
    boolean existsWarehouseCode(String warehouseCode, Long excludeId);

    /**
     * 获取库室及其物资数量
     */
    List<Warehouse> getWarehouseWithMaterialCount();

    // ========== 统计分析功能 ==========

    /**
     * 获取库房统计信息
     */
    Map<String, Object> getWarehouseStatistics();

    /**
     * 获取库房利用率分析
     */
    Map<String, Object> getWarehouseUtilization();
}

