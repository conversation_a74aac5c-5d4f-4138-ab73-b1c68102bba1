package com.fadun.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.common.exception.BusinessException;
import com.fadun.common.result.PageResult;
import com.fadun.entity.InboundOrder;
import com.fadun.entity.InboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;
import com.fadun.entity.MaterialStock;
import com.fadun.mapper.InboundMapper;
import com.fadun.service.InboundManagementService;
import com.fadun.service.WarehouseManagementService;
import com.fadun.service.MaterialManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 物资入库管理服务实现类
 * 整合入库流程和明细管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InboundManagementServiceImpl implements InboundManagementService {

    private final InboundMapper inboundMapper;
    private final WarehouseManagementService warehouseManagementService;
    private final MaterialManagementService materialManagementService;

    // ========== 核心入库功能 ==========

    @Override
    public List<Warehouse> getWarehouseList() {
        log.info("获取库室列表");
        
        try {
            // 调用库房管理服务获取库室列表
            List<Warehouse> warehouses = warehouseManagementService.getWarehouseList();
            log.info("获取库室列表成功: 数量={}", warehouses.size());
            return warehouses;
        } catch (Exception e) {
            log.error("获取库室列表失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取库室列表失败");
        }
    }

    @Override
    public List<Material> getMaterialsByWarehouse(Long warehouseId) {
        log.info("根据库室获取物资: warehouseId={}", warehouseId);
        
        if (warehouseId == null || warehouseId <= 0) {
            throw BusinessException.paramError("库室ID不能为空");
        }
        
        try {
            // 调用物资管理服务获取指定库房的物资列表
            List<Material> materials = materialManagementService.getMaterialsByWarehouse(warehouseId);
            log.info("根据库室获取物资成功: warehouseId={}, 物资数量={}", warehouseId, materials.size());
            return materials;
        } catch (Exception e) {
            log.error("根据库室获取物资失败: warehouseId={}, error={}", warehouseId, e.getMessage(), e);
            throw BusinessException.of("获取库室物资失败");
        }
    }

    @Override
    @Transactional
    public boolean submitInboundOrder(InboundOrder inboundOrder) {
        log.info("提交入库单: {}", inboundOrder);
        
        // 业务数据验证
        validateInboundOrder(inboundOrder);
        
        try {
            // 1. 生成入库单号
            if (!StringUtils.hasText(inboundOrder.getOrderNo())) {
                inboundOrder.setOrderNo(generateInboundOrderNo());
            }
            
            // 2. 设置基础信息
            inboundOrder.setStatus(1); // 待入库状态
            inboundOrder.setInboundTime(LocalDateTime.now());
            
            // 3. 计算总数量
            if (inboundOrder.getDetails() != null && !inboundOrder.getDetails().isEmpty()) {
                int totalQuantity = inboundOrder.getDetails().stream()
                        .mapToInt(detail -> detail.getQuantity())
                        .sum();
                inboundOrder.setTotalQuantity(totalQuantity);
            }
            
            // 4. 保存入库单主表
            int result = inboundMapper.insert(inboundOrder);
            if (result <= 0) {
                throw BusinessException.of("创建入库单失败");
            }
            
            // 5. 保存入库明细
            if (inboundOrder.getDetails() != null && !inboundOrder.getDetails().isEmpty()) {
                // 设置入库单ID到明细中
                for (InboundDetail detail : inboundOrder.getDetails()) {
                    detail.setOrderId(inboundOrder.getId());
                }
                
                boolean detailsSaved = saveDetails(inboundOrder.getDetails());
                if (!detailsSaved) {
                    throw BusinessException.of("保存入库明细失败");
                }
            }
            
            // 6. 自动确认入库（更新库存）
            boolean confirmed = confirmInbound(inboundOrder.getId());
            if (!confirmed) {
                throw BusinessException.of("确认入库失败");
            }
            
            log.info("提交入库单成功: orderNo={}, id={}", inboundOrder.getOrderNo(), inboundOrder.getId());
            return true;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交入库单失败: {}", e.getMessage(), e);
            throw BusinessException.of("提交入库单失败: " + e.getMessage());
        }
    }

        @Override
    public PageResult<InboundOrder> getInboundRecords(Long current, Long size, String keyword, 
                                                     Long warehouseId, String startDate, String endDate) {
        log.info("获取入库记录: current={}, size={}, keyword={}, warehouseId={}, startDate={}, endDate={}", 
                current, size, keyword, warehouseId, startDate, endDate);
        
        // 参数验证
        if (current < 1) current = 1L;
        if (size < 1 || size > 100) size = 10L;
        
        try {
            Page<InboundOrder> page = new Page<>(current, size);
            Page<InboundOrder> result = inboundMapper.selectInboundRecords(page, keyword, warehouseId, startDate, endDate);
            
            log.info("获取入库记录成功: 总数={}, 当前页数据={}", result.getTotal(), result.getRecords().size());
            return PageResult.fromPage(result);
        } catch (Exception e) {
            log.error("获取入库记录失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取入库记录失败");
        }
    }

    // ========== 入库单明细管理 ==========

    @Override
    public List<InboundDetail> getDetailsByOrderId(Long orderId) {
        log.info("根据订单ID获取明细: orderId={}", orderId);
        
        if (orderId == null || orderId <= 0) {
            throw BusinessException.paramError("入库单ID不能为空");
        }
        
        try {
            List<InboundDetail> details = inboundMapper.selectDetailsByOrderId(orderId);
            log.info("根据订单ID获取明细成功: orderId={}, 明细数量={}", orderId, details.size());
            return details;
        } catch (Exception e) {
            log.error("根据订单ID获取明细失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw BusinessException.of("获取入库明细失败");
        }
    }

    @Override
    @Transactional
    public boolean saveDetails(List<InboundDetail> details) {
        log.info("保存入库明细: 数量={}", details != null ? details.size() : 0);
        
        if (details == null || details.isEmpty()) {
            throw BusinessException.paramError("入库明细不能为空");
        }
        
        // 验证明细数据
        for (InboundDetail detail : details) {
            if (detail.getOrderId() == null || detail.getOrderId() <= 0) {
                throw BusinessException.paramError("入库明细中的订单ID不能为空");
            }
            if (detail.getMaterialId() == null || detail.getMaterialId() <= 0) {
                throw BusinessException.paramError("入库明细中的物资ID不能为空");
            }
            if (detail.getQuantity() == null || detail.getQuantity() <= 0) {
                throw BusinessException.paramError("入库明细中的数量必须大于0");
            }
        }
        
        try {
            int result = inboundMapper.insertBatch(details);
            boolean success = result > 0;
            
            if (success) {
                log.info("保存入库明细成功: 保存数量={}", result);
            }
            
            return success;
        } catch (Exception e) {
            log.error("保存入库明细失败: {}", e.getMessage(), e);
            throw BusinessException.of("保存入库明细失败: " + e.getMessage());
        }
    }

    // ========== 传统入库管理功能（保留兼容） ==========

    @Override
    public PageResult<InboundOrder> getInboundPage(Long current, Long size, String keyword, String inboundType, Integer status) {
        log.info("获取入库分页数据: current={}, size={}, keyword={}, inboundType={}, status={}", 
                current, size, keyword, inboundType, status);
        
        // 参数验证
        if (current < 1) current = 1L;
        if (size < 1 || size > 100) size = 10L;
        
        try {
            Page<InboundOrder> page = new Page<>(current, size);
            Page<InboundOrder> result = inboundMapper.selectInboundPage(page, keyword, inboundType, status);
            
            log.info("获取入库分页数据成功: 总数={}, 当前页数据={}", result.getTotal(), result.getRecords().size());
            return PageResult.fromPage(result);
        } catch (Exception e) {
            log.error("获取入库分页数据失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取入库分页数据失败");
        }
    }

    @Override
    @Transactional
    public boolean createInboundOrder(InboundOrder inboundOrder) {
        log.info("创建入库单: {}", inboundOrder);
        
        // 业务数据验证
        validateInboundOrder(inboundOrder);
        
        try {
            // 1. 生成入库单号
            if (!StringUtils.hasText(inboundOrder.getOrderNo())) {
                inboundOrder.setOrderNo(generateInboundOrderNo());
            }
            
            // 2. 设置基础信息
            inboundOrder.setStatus(1); // 待入库状态
            
            // 3. 保存入库单
            int result = inboundMapper.insert(inboundOrder);
            boolean success = result > 0;
            
            if (success) {
                log.info("创建入库单成功: orderNo={}, id={}", inboundOrder.getOrderNo(), inboundOrder.getId());
            }
            
            return success;
        } catch (Exception e) {
            log.error("创建入库单失败: {}", e.getMessage(), e);
            throw BusinessException.of("创建入库单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean confirmInbound(Long orderId) {
        log.info("确认入库: orderId={}", orderId);
        
        if (orderId == null || orderId <= 0) {
            throw BusinessException.paramError("入库单ID不能为空");
        }
        
        try {
            // 1. 检查入库单是否存在
            InboundOrder inboundOrder = inboundMapper.selectById(orderId);
            if (inboundOrder == null) {
                throw BusinessException.dataNotFound("入库单不存在: ID=" + orderId);
            }
            
            // 2. 检查入库单状态
            if (inboundOrder.getStatus() != 1) {
                throw BusinessException.of("只有待入库状态的单据才能确认入库");
            }
            
            // 3. 获取入库明细
            List<InboundDetail> details = inboundMapper.selectDetailsByOrderId(orderId);
            if (details.isEmpty()) {
                throw BusinessException.of("入库明细不能为空");
            }
            
            // 4. 更新库存
            updateStockForInbound(inboundOrder.getWarehouseId(), details);
            
            // 5. 更新入库单状态为已入库
            inboundOrder.setStatus(2);
            inboundOrder.setInboundTime(LocalDateTime.now());
            int updateResult = inboundMapper.updateById(inboundOrder);
            
            if (updateResult <= 0) {
                throw BusinessException.of("更新入库单状态失败");
            }
            
            log.info("确认入库成功: orderId={}, 明细数量={}", orderId, details.size());
            return true;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("确认入库失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw BusinessException.of("确认入库失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean cancelInbound(Long orderId) {
        log.info("取消入库: orderId={}", orderId);
        
        if (orderId == null || orderId <= 0) {
            throw BusinessException.paramError("入库单ID不能为空");
        }
        
        try {
            // 1. 检查入库单是否存在
            InboundOrder inboundOrder = inboundMapper.selectById(orderId);
            if (inboundOrder == null) {
                throw BusinessException.dataNotFound("入库单不存在: ID=" + orderId);
            }
            
            // 2. 检查入库单状态
            if (inboundOrder.getStatus() == 3) {
                throw BusinessException.of("入库单已取消");
            }
            if (inboundOrder.getStatus() == 2) {
                throw BusinessException.of("已入库的单据不能取消");
            }
            
            // 3. 更新入库单状态为已取消
            inboundOrder.setStatus(3);
            int updateResult = inboundMapper.updateById(inboundOrder);
            
            if (updateResult <= 0) {
                throw BusinessException.of("取消入库单失败");
            }
            
            log.info("取消入库成功: orderId={}", orderId);
            return true;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("取消入库失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw BusinessException.of("取消入库失败: " + e.getMessage());
        }
    }

    @Override
    public InboundOrder getInboundDetail(Long orderId) {
        log.info("获取入库详情: orderId={}", orderId);
        
        if (orderId == null || orderId <= 0) {
            throw BusinessException.paramError("入库单ID不能为空");
        }
        
        try {
            // 使用增强的查询方法，包含库房信息
            InboundOrder inboundOrder = inboundMapper.selectInboundDetailById(orderId);
            
            if (inboundOrder == null) {
                throw BusinessException.dataNotFound("入库单不存在: ID=" + orderId);
            }
            
            // 获取入库明细
            List<InboundDetail> details = inboundMapper.selectDetailsByOrderId(orderId);
            inboundOrder.setDetails(details);
            
            log.info("获取入库详情成功: orderId={}, 明细数量={}", orderId, details.size());
            return inboundOrder;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取入库详情失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw BusinessException.of("获取入库详情失败");
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成入库单号
     * 格式: RK + YYYYMMDD + 6位序号 (如: RK20241220000001)
     */
    private String generateInboundOrderNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "RK" + dateStr;
        
        // TODO: 后续可以改为从数据库查询当日最大序号 + 1
        // 这里简化处理，使用时间戳后6位
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = timestamp.substring(timestamp.length() - 6);
        
        return prefix + suffix;
    }

    /**
     * 验证入库单数据
     */
    private void validateInboundOrder(InboundOrder inboundOrder) {
        if (inboundOrder == null) {
            throw BusinessException.paramError("入库单信息不能为空");
        }
        
        if (inboundOrder.getWarehouseId() == null || inboundOrder.getWarehouseId() <= 0) {
            throw BusinessException.paramError("库房ID不能为空");
        }
        
        if (!StringUtils.hasText(inboundOrder.getSupplier())) {
            throw BusinessException.paramError("供应商不能为空");
        }
        
        if (!StringUtils.hasText(inboundOrder.getInboundType())) {
            inboundOrder.setInboundType("purchase"); // 默认为采购入库
        }
        
        // 验证入库类型是否合法
        if (!Arrays.asList("purchase", "return", "transfer").contains(inboundOrder.getInboundType())) {
            throw BusinessException.paramError("入库类型无效，支持的类型: purchase, return, transfer");
        }
        
        if (!StringUtils.hasText(inboundOrder.getOperatorName())) {
            throw BusinessException.paramError("操作员姓名不能为空");
        }
        
        // 如果有明细，验证明细数据
        if (inboundOrder.getDetails() != null) {
            for (InboundDetail detail : inboundOrder.getDetails()) {
                if (detail.getMaterialId() == null || detail.getMaterialId() <= 0) {
                    throw BusinessException.paramError("入库明细中的物资ID不能为空");
                }
                if (detail.getQuantity() == null || detail.getQuantity() <= 0) {
                    throw BusinessException.paramError("入库明细中的数量必须大于0");
                }
            }
        }
    }

    /**
     * 更新库存（入库操作）
     */
    private void updateStockForInbound(Long warehouseId, List<InboundDetail> details) {
        log.info("更新库存 - 入库操作: warehouseId={}, 明细数量={}", warehouseId, details.size());
        
        try {
            for (InboundDetail detail : details) {
                // TODO: 这里应该调用库存管理服务更新库存
                // 当前先记录日志，后续集成库存更新功能
                log.info("更新库存: materialId={}, warehouseId={}, quantity=+{}", 
                        detail.getMaterialId(), warehouseId, detail.getQuantity());
                
                // materialStockService.addStock(detail.getMaterialId(), warehouseId, detail.getQuantity());
            }
            
            log.info("库存更新完成 - 入库操作");
        } catch (Exception e) {
            log.error("更新库存失败: {}", e.getMessage(), e);
            throw BusinessException.of("更新库存失败: " + e.getMessage());
        }
    }
}

