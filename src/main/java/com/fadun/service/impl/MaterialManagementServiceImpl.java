package com.fadun.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fadun.common.exception.BusinessException;
import com.fadun.common.result.PageResult;
import com.fadun.entity.Material;
import com.fadun.entity.MaterialCategory;
import com.fadun.entity.MaterialStock;
import com.fadun.mapper.MaterialMapper;
import com.fadun.service.MaterialManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 物资台账管理服务实现类
 * 整合物资信息、分类管理、库存查询功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialManagementServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialManagementService {

    private final MaterialMapper materialMapper;

    // ========== 物资信息管理 ==========

    @Override
    public PageResult<Material> getMaterialPage(Long current, Long size, String keyword, Long categoryId, Long warehouseId) {
        log.info("获取物资分页数据: current={}, size={}, keyword={}, categoryId={}, warehouseId={}", 
                current, size, keyword, categoryId, warehouseId);
        
        // 参数验证
        if (current < 1) current = 1L;
        if (size < 1 || size > 100) size = 10L;
        
        try {
            Page<Material> page = new Page<>(current, size);
            Page<Material> result = materialMapper.selectMaterialPage(page, keyword, categoryId, warehouseId);
            
            log.info("物资分页查询成功: 总数={}, 当前页数据={}", result.getTotal(), result.getRecords().size());
            return PageResult.fromPage(result);
        } catch (Exception e) {
            log.error("物资分页查询失败: {}", e.getMessage(), e);
            throw BusinessException.of("物资分页查询失败");
        }
    }

    @Override
    @Transactional
    public boolean createMaterial(Material material) {
        log.info("创建物资开始: {}", material);
        
        // 业务验证
        validateMaterialForCreate(material);
        
        // 检查物资编码是否重复
        if (StringUtils.hasText(material.getMaterialCode())) {
            int count = materialMapper.countByMaterialCode(material.getMaterialCode(), null);
            if (count > 0) {
                throw BusinessException.paramError("物资编码已存在: " + material.getMaterialCode());
            }
        }
        
        // 设置默认值
        if (material.getStatus() == null) {
            material.setStatus(1); // 默认正常状态
        }
        
        try {
            boolean result = save(material);
            if (result) {
                log.info("创建物资成功: materialCode={}, id={}", material.getMaterialCode(), material.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("创建物资失败: materialCode={}, error={}", material.getMaterialCode(), e.getMessage(), e);
            throw BusinessException.of("创建物资失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateMaterial(Material material) {
        log.info("更新物资开始: {}", material);
        
        // 检查物资是否存在
        Material existingMaterial = getById(material.getId());
        if (existingMaterial == null) {
            throw BusinessException.dataNotFound("物资不存在: ID=" + material.getId());
        }
        
        // 业务验证
        validateMaterialForUpdate(material);
        
        // 检查物资编码是否重复（排除自身）
        if (StringUtils.hasText(material.getMaterialCode())) {
            int count = materialMapper.countByMaterialCode(material.getMaterialCode(), material.getId());
            if (count > 0) {
                throw BusinessException.paramError("物资编码已存在: " + material.getMaterialCode());
            }
        }
        
        try {
            boolean result = updateById(material);
            if (result) {
                log.info("更新物资成功: materialCode={}, id={}", material.getMaterialCode(), material.getId());
            }
            return result;
        } catch (Exception e) {
            log.error("更新物资失败: materialCode={}, error={}", material.getMaterialCode(), e.getMessage(), e);
            throw BusinessException.of("更新物资失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteMaterial(Long id) {
        log.info("删除物资开始: id={}", id);
        
        if (id == null || id <= 0) {
            throw BusinessException.paramError("物资ID不能为空");
        }
        
        // 检查物资是否存在
        Material material = getById(id);
        if (material == null) {
            throw BusinessException.dataNotFound("物资不存在: ID=" + id);
        }
        
        // 检查是否有库存记录
        // TODO: 后续实现库存检查逻辑
        // boolean hasStock = materialStockService.hasStockByMaterialId(id);
        // if (hasStock) {
        //     throw BusinessException.of("物资存在库存记录，无法删除");
        // }
        
        try {
            boolean result = removeById(id);
            if (result) {
                log.info("删除物资成功: materialCode={}, id={}", material.getMaterialCode(), id);
            }
            return result;
        } catch (Exception e) {
            log.error("删除物资失败: id={}, error={}", id, e.getMessage(), e);
            throw BusinessException.of("删除物资失败: " + e.getMessage());
        }
    }

    @Override
    public Material getMaterialDetail(Long id) {
        log.info("获取物资详情: id={}", id);
        
        if (id == null || id <= 0) {
            throw BusinessException.paramError("物资ID不能为空");
        }
        
        try {
            // 使用增强的查询方法，包含分类信息和库存汇总
            Material material = materialMapper.selectMaterialDetailById(id);
            if (material == null) {
                throw BusinessException.dataNotFound("物资不存在: ID=" + id);
            }
            
            log.info("获取物资详情成功: materialCode={}", material.getMaterialCode());
            return material;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取物资详情失败: id={}, error={}", id, e.getMessage(), e);
            throw BusinessException.of("获取物资详情失败");
        }
    }

    @Override
    public List<Material> getMaterialsByWarehouse(Long warehouseId) {
        log.info("根据库室获取物资: warehouseId={}", warehouseId);
        
        if (warehouseId == null || warehouseId <= 0) {
            throw BusinessException.paramError("库室ID不能为空");
        }
        
        try {
            List<Material> materials = materialMapper.selectMaterialsByWarehouse(warehouseId);
            log.info("根据库室获取物资成功: warehouseId={}, count={}", warehouseId, materials.size());
            return materials;
        } catch (Exception e) {
            log.error("根据库室获取物资失败: warehouseId={}, error={}", warehouseId, e.getMessage(), e);
            throw BusinessException.of("获取库室物资失败");
        }
    }

    // ========== 物资分类管理 ==========

    @Override
    public List<MaterialCategory> getCategoryTree() {
        log.info("获取分类树结构");
        
        try {
            // 获取所有分类（带物资数量）
            List<MaterialCategory> allCategories = materialMapper.selectCategoryWithMaterialCount();
            
            // 构建树形结构
            List<MaterialCategory> rootCategories = buildCategoryTree(allCategories, 0L);
            
            log.info("获取分类树结构成功: 根分类数量={}", rootCategories.size());
            return rootCategories;
        } catch (Exception e) {
            log.error("获取分类树结构失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取分类树结构失败");
        }
    }

    @Override
    public List<MaterialCategory> getCategoryList() {
        log.info("获取分类列表");
        
        try {
            List<MaterialCategory> categories = materialMapper.selectAllCategories();
            log.info("获取分类列表成功: 数量={}", categories.size());
            return categories;
        } catch (Exception e) {
            log.error("获取分类列表失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取分类列表失败");
        }
    }

    @Override
    public List<MaterialCategory> getChildrenCategories(Long parentId) {
        log.info("获取子分类: parentId={}", parentId);
        
        if (parentId == null || parentId < 0) {
            throw BusinessException.paramError("父分类ID不能为空");
        }
        
        try {
            List<MaterialCategory> children = materialMapper.selectCategoriesByParentId(parentId);
            log.info("获取子分类成功: parentId={}, 子分类数量={}", parentId, children.size());
            return children;
        } catch (Exception e) {
            log.error("获取子分类失败: parentId={}, error={}", parentId, e.getMessage(), e);
            throw BusinessException.of("获取子分类失败");
        }
    }

    @Override
    public MaterialCategory getCategoryDetail(Long id) {
        log.info("获取分类详情: id={}", id);
        
        if (id == null || id <= 0) {
            throw BusinessException.paramError("分类ID不能为空");
        }
        
        try {
            MaterialCategory category = materialMapper.selectCategoryById(id);
            if (category == null) {
                throw BusinessException.dataNotFound("分类不存在: ID=" + id);
            }
            
            log.info("获取分类详情成功: categoryCode={}", category.getCategoryCode());
            return category;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取分类详情失败: id={}, error={}", id, e.getMessage(), e);
            throw BusinessException.of("获取分类详情失败");
        }
    }

    @Override
    @Transactional
    public boolean createCategory(MaterialCategory category) {
        log.info("创建分类开始: {}", category);
        
        // 业务验证
        validateCategoryForCreate(category);
        
        // 检查分类编码是否重复
        if (StringUtils.hasText(category.getCategoryCode())) {
            int count = materialMapper.countByCategoryCode(category.getCategoryCode(), null);
            if (count > 0) {
                throw BusinessException.paramError("分类编码已存在: " + category.getCategoryCode());
            }
        }
        
        // 计算分类层级
        if (category.getParentId() != null && category.getParentId() > 0) {
            MaterialCategory parentCategory = materialMapper.selectCategoryById(category.getParentId());
            if (parentCategory == null) {
                throw BusinessException.dataNotFound("父分类不存在: ID=" + category.getParentId());
            }
            category.setLevel(parentCategory.getLevel() + 1);
        } else {
            category.setParentId(0L); // 顶级分类
            category.setLevel(1);
        }
        
        try {
            int result = materialMapper.insertCategory(category);
            if (result > 0) {
                log.info("创建分类成功: categoryCode={}, id={}", category.getCategoryCode(), category.getId());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("创建分类失败: categoryCode={}, error={}", category.getCategoryCode(), e.getMessage(), e);
            throw BusinessException.of("创建分类失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateCategory(MaterialCategory category) {
        log.info("更新分类开始: {}", category);
        
        // 检查分类是否存在
        MaterialCategory existingCategory = materialMapper.selectCategoryById(category.getId());
        if (existingCategory == null) {
            throw BusinessException.dataNotFound("分类不存在: ID=" + category.getId());
        }
        
        // 业务验证
        validateCategoryForUpdate(category);
        
        // 检查分类编码是否重复（排除自身）
        if (StringUtils.hasText(category.getCategoryCode())) {
            int count = materialMapper.countByCategoryCode(category.getCategoryCode(), category.getId());
            if (count > 0) {
                throw BusinessException.paramError("分类编码已存在: " + category.getCategoryCode());
            }
        }
        
        // 检查父分类循环引用
        if (category.getParentId() != null && category.getParentId() > 0) {
            if (category.getParentId().equals(category.getId())) {
                throw BusinessException.paramError("不能将自己设为父分类");
            }
            
            // 检查是否会造成循环引用
            if (isCircularReference(category.getId(), category.getParentId())) {
                throw BusinessException.paramError("不能设置子分类为父分类，会造成循环引用");
            }
            
            // 重新计算层级
            MaterialCategory parentCategory = materialMapper.selectCategoryById(category.getParentId());
            if (parentCategory == null) {
                throw BusinessException.dataNotFound("父分类不存在: ID=" + category.getParentId());
            }
            category.setLevel(parentCategory.getLevel() + 1);
        } else {
            category.setParentId(0L);
            category.setLevel(1);
        }
        
        try {
            int result = materialMapper.updateCategoryById(category);
            if (result > 0) {
                log.info("更新分类成功: categoryCode={}, id={}", category.getCategoryCode(), category.getId());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新分类失败: categoryCode={}, error={}", category.getCategoryCode(), e.getMessage(), e);
            throw BusinessException.of("更新分类失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteCategory(Long id) {
        log.info("删除分类开始: id={}", id);
        
        if (id == null || id <= 0) {
            throw BusinessException.paramError("分类ID不能为空");
        }
        
        // 检查分类是否存在
        MaterialCategory category = materialMapper.selectCategoryById(id);
        if (category == null) {
            throw BusinessException.dataNotFound("分类不存在: ID=" + id);
        }
        
        // 检查是否有子分类
        int childrenCount = materialMapper.countChildrenByCategory(id);
        if (childrenCount > 0) {
            throw BusinessException.of("该分类下存在子分类，请先删除子分类");
        }
        
        // 检查是否有物资
        int materialCount = materialMapper.countMaterialsInCategory(id);
        if (materialCount > 0) {
            throw BusinessException.of("该分类下存在物资记录，无法删除");
        }
        
        try {
            int result = materialMapper.deleteCategoryById(id);
            if (result > 0) {
                log.info("删除分类成功: categoryCode={}, id={}", category.getCategoryCode(), id);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除分类失败: id={}, error={}", id, e.getMessage(), e);
            throw BusinessException.of("删除分类失败: " + e.getMessage());
        }
    }

    @Override
    public boolean existsCategoryCode(String categoryCode, Long excludeId) {
        log.info("检查分类编码是否存在: categoryCode={}, excludeId={}", categoryCode, excludeId);
        
        if (!StringUtils.hasText(categoryCode)) {
            return false;
        }
        
        try {
            int count = materialMapper.countByCategoryCode(categoryCode, excludeId);
            return count > 0;
        } catch (Exception e) {
            log.error("检查分类编码失败: categoryCode={}, error={}", categoryCode, e.getMessage(), e);
            return false;
        }
    }

    // ========== 库存查询功能 ==========

    @Override
    public List<MaterialStock> getMaterialWithTotalStock() {
        log.info("获取物资总库存");
        
        try {
            // 获取所有物资的总库存信息
            List<MaterialStock> materials = materialMapper.selectMaterialWithTotalStock(null, null);
            log.info("获取物资总库存成功: 物资数量={}", materials.size());
            return materials;
        } catch (Exception e) {
            log.error("获取物资总库存失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取物资总库存失败");
        }
    }

    @Override
    public boolean checkStockSufficient(Long materialId, Long warehouseId, Integer quantity) {
        log.info("检查库存是否充足: materialId={}, warehouseId={}, quantity={}", materialId, warehouseId, quantity);
        
        // 参数验证
        if (materialId == null || materialId <= 0) {
            throw BusinessException.paramError("物资ID不能为空");
        }
        if (warehouseId == null || warehouseId <= 0) {
            throw BusinessException.paramError("库房ID不能为空");
        }
        if (quantity == null || quantity <= 0) {
            throw BusinessException.paramError("数量必须大于0");
        }
        
        try {
            // 查询指定物资在指定库房的库存
            MaterialStock stock = materialMapper.selectStockByMaterialAndWarehouse(materialId, warehouseId);
            
            if (stock == null) {
                log.warn("物资库存记录不存在: materialId={}, warehouseId={}", materialId, warehouseId);
                return false;
            }
            
            boolean sufficient = stock.getCurrentStock() >= quantity;
            log.info("库存充足性检查结果: materialId={}, warehouseId={}, 需求量={}, 现有库存={}, 是否充足={}", 
                    materialId, warehouseId, quantity, stock.getCurrentStock(), sufficient);
            
            return sufficient;
        } catch (Exception e) {
            log.error("检查库存充足性失败: materialId={}, warehouseId={}, error={}", materialId, warehouseId, e.getMessage(), e);
            throw BusinessException.of("检查库存充足性失败");
        }
    }

    @Override
    public PageResult<MaterialStock> getStockPage(Long current, Long size, String keyword,
                                                 Long materialId, Long warehouseId, Long categoryId, String stockStatus) {
        log.info("获取库存分页数据: current={}, size={}, keyword={}, materialId={}, warehouseId={}, categoryId={}, stockStatus={}", 
                current, size, keyword, materialId, warehouseId, categoryId, stockStatus);
        
        // 参数验证
        if (current < 1) current = 1L;
        if (size < 1 || size > 100) size = 10L;
        
        // 验证库存状态参数
        if (StringUtils.hasText(stockStatus)) {
            if (!Arrays.asList("zero", "low", "normal").contains(stockStatus)) {
                throw BusinessException.paramError("库存状态参数无效，支持的值: zero, low, normal");
            }
        }
        
        try {
            Page<MaterialStock> page = new Page<>(current, size);
            Page<MaterialStock> result = materialMapper.selectStockPage(page, keyword, materialId, warehouseId, categoryId, stockStatus);
            
            log.info("库存分页查询成功: 总数={}, 当前页数据={}", result.getTotal(), result.getRecords().size());
            return PageResult.fromPage(result);
        } catch (Exception e) {
            log.error("库存分页查询失败: {}", e.getMessage(), e);
            throw BusinessException.of("库存分页查询失败");
        }
    }

    // ========== 统计分析功能 ==========

    @Override
    public Map<String, Object> getMaterialStatistics() {
        log.info("获取物资统计信息");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取基础统计数据
            Map<String, Object> overview = materialMapper.getMaterialStatisticsOverview();
            result.putAll(overview);
            
            // 获取分类统计
            List<Map<String, Object>> categoryStats = materialMapper.countMaterialsByCategory();
            result.put("categoryStatistics", categoryStats);
            
            // 获取状态统计
            List<Map<String, Object>> statusStats = materialMapper.countMaterialsByStatus();
            result.put("statusStatistics", statusStats);
            
            // 获取库存不足物资
            List<Map<String, Object>> lowStockMaterials = materialMapper.selectLowStockMaterials();
            result.put("lowStockMaterials", lowStockMaterials);
            result.put("lowStockCount", lowStockMaterials.size());
            
            log.info("获取物资统计信息成功");
            return result;
        } catch (Exception e) {
            log.error("获取物资统计信息失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取物资统计信息失败");
        }
    }

    @Override
    public Map<String, Object> getResourceAnalysis() {
        log.info("获取资源分析信息");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取基础统计
            Map<String, Object> overview = materialMapper.getMaterialStatisticsOverview();
            
            // 计算资源实力指标（安全的数字类型转换）
            int totalMaterials = parseIntSafely(overview.get("totalMaterials"));
            int activeMaterials = parseIntSafely(overview.get("activeMaterials"));
            int totalCategories = parseIntSafely(overview.get("totalCategories"));
            
            // 资源配置合理性分析
            double activeMaterialRatio = totalMaterials > 0 ? (double) activeMaterials / totalMaterials : 0;
            
            result.put("totalMaterials", totalMaterials);
            result.put("activeMaterials", activeMaterials);
            result.put("totalCategories", totalCategories);
            result.put("activeMaterialRatio", Math.round(activeMaterialRatio * 10000) / 100.0); // 保留2位小数
            
            // 资源分布分析
            List<Map<String, Object>> categoryStats = materialMapper.countMaterialsByCategory();
            result.put("categoryDistribution", categoryStats);
            
            // 库存不足分析
            List<Map<String, Object>> lowStockMaterials = materialMapper.selectLowStockMaterials();
            result.put("lowStockAlert", lowStockMaterials.size());
            result.put("lowStockDetails", lowStockMaterials);
            
            // 资源实力评级
            String resourceLevel = calculateResourceLevel(totalMaterials, activeMaterials, totalCategories, lowStockMaterials.size());
            result.put("resourceLevel", resourceLevel);
            
            log.info("获取资源分析信息成功");
            return result;
        } catch (Exception e) {
            log.error("获取资源分析信息失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取资源分析信息失败");
        }
    }

    @Override
    public Object exportMaterial(String keyword, Long categoryId, Long warehouseId) {
        log.info("导出物资: keyword={}, categoryId={}, warehouseId={}", keyword, categoryId, warehouseId);
        
        try {
            // 获取要导出的物资数据
            Page<Material> page = new Page<>(1, 10000); // 导出时设置较大的分页大小
            Page<Material> result = materialMapper.selectMaterialPage(page, keyword, categoryId, warehouseId);
            
            List<Material> materials = result.getRecords();
            
            // TODO: 集成EasyExcel实现Excel导出
            // 这里先返回数据结构，后续集成Excel导出功能
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("materials", materials);
            exportData.put("totalCount", result.getTotal());
            exportData.put("exportTime", LocalDateTime.now());
            Map<String, Object> exportConditions = new HashMap<>();
            exportConditions.put("keyword", keyword);
            exportConditions.put("categoryId", categoryId);
            exportConditions.put("warehouseId", warehouseId);
            exportData.put("exportConditions", exportConditions);
            
            log.info("导出物资数据准备完成: 共{}条记录", materials.size());
            return exportData;
        } catch (Exception e) {
            log.error("导出物资失败: {}", e.getMessage(), e);
            throw BusinessException.of("导出物资失败");
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 验证物资创建数据
     */
    private void validateMaterialForCreate(Material material) {
        if (material == null) {
            throw BusinessException.paramError("物资信息不能为空");
        }
        if (!StringUtils.hasText(material.getMaterialName())) {
            throw BusinessException.paramError("物资名称不能为空");
        }
        if (!StringUtils.hasText(material.getMaterialCode())) {
            throw BusinessException.paramError("物资编码不能为空");
        }
        if (material.getCategoryId() == null || material.getCategoryId() <= 0) {
            throw BusinessException.paramError("物资分类不能为空");
        }
        if (!StringUtils.hasText(material.getUnit())) {
            throw BusinessException.paramError("计量单位不能为空");
        }
    }

    /**
     * 验证物资更新数据
     */
    private void validateMaterialForUpdate(Material material) {
        if (material == null) {
            throw BusinessException.paramError("物资信息不能为空");
        }
        if (material.getId() == null || material.getId() <= 0) {
            throw BusinessException.paramError("物资ID不能为空");
        }
        if (!StringUtils.hasText(material.getMaterialName())) {
            throw BusinessException.paramError("物资名称不能为空");
        }
        if (!StringUtils.hasText(material.getMaterialCode())) {
            throw BusinessException.paramError("物资编码不能为空");
        }
        if (material.getCategoryId() == null || material.getCategoryId() <= 0) {
            throw BusinessException.paramError("物资分类不能为空");
        }
        if (!StringUtils.hasText(material.getUnit())) {
            throw BusinessException.paramError("计量单位不能为空");
        }
    }

    /**
     * 计算资源实力等级
     */
    private String calculateResourceLevel(int totalMaterials, int activeMaterials, int totalCategories, int lowStockCount) {
        // 简单的评级算法，后续可以根据业务需求调整
        if (totalMaterials >= 1000 && activeMaterials >= 800 && totalCategories >= 20 && lowStockCount <= 10) {
            return "优秀";
        } else if (totalMaterials >= 500 && activeMaterials >= 400 && totalCategories >= 10 && lowStockCount <= 20) {
            return "良好";
        } else if (totalMaterials >= 200 && activeMaterials >= 150 && totalCategories >= 5 && lowStockCount <= 50) {
            return "一般";
        } else {
            return "待改善";
        }
    }

    // ========== 分类管理私有辅助方法 ==========

    /**
     * 验证分类创建数据
     */
    private void validateCategoryForCreate(MaterialCategory category) {
        if (category == null) {
            throw BusinessException.paramError("分类信息不能为空");
        }
        if (!StringUtils.hasText(category.getCategoryName())) {
            throw BusinessException.paramError("分类名称不能为空");
        }
        if (!StringUtils.hasText(category.getCategoryCode())) {
            throw BusinessException.paramError("分类编码不能为空");
        }
        
        // 编码格式验证（可根据需要调整）
        if (category.getCategoryCode().length() > 20) {
            throw BusinessException.paramError("分类编码长度不能超过20个字符");
        }
        
        // 名称长度验证
        if (category.getCategoryName().length() > 50) {
            throw BusinessException.paramError("分类名称长度不能超过50个字符");
        }
    }

    /**
     * 验证分类更新数据
     */
    private void validateCategoryForUpdate(MaterialCategory category) {
        if (category == null) {
            throw BusinessException.paramError("分类信息不能为空");
        }
        if (category.getId() == null || category.getId() <= 0) {
            throw BusinessException.paramError("分类ID不能为空");
        }
        if (!StringUtils.hasText(category.getCategoryName())) {
            throw BusinessException.paramError("分类名称不能为空");
        }
        if (!StringUtils.hasText(category.getCategoryCode())) {
            throw BusinessException.paramError("分类编码不能为空");
        }
        
        // 编码格式验证
        if (category.getCategoryCode().length() > 20) {
            throw BusinessException.paramError("分类编码长度不能超过20个字符");
        }
        
        // 名称长度验证
        if (category.getCategoryName().length() > 50) {
            throw BusinessException.paramError("分类名称长度不能超过50个字符");
        }
    }

    /**
     * 检查循环引用
     * 递归检查指定分类的所有子分类中是否包含目标父分类ID
     */
    private boolean isCircularReference(Long categoryId, Long targetParentId) {
        if (categoryId == null || targetParentId == null) {
            return false;
        }
        
        // 获取当前分类的所有子分类
        List<MaterialCategory> children = materialMapper.selectCategoriesByParentId(categoryId);
        
        for (MaterialCategory child : children) {
            // 如果子分类ID等于目标父分类ID，说明会造成循环引用
            if (child.getId().equals(targetParentId)) {
                return true;
            }
            // 递归检查子分类的子分类
            if (isCircularReference(child.getId(), targetParentId)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 构建分类树形结构
     * 递归构建以指定父分类ID为根的分类树
     */
    private List<MaterialCategory> buildCategoryTree(List<MaterialCategory> allCategories, Long parentId) {
        List<MaterialCategory> result = new ArrayList<>();
        
        if (allCategories == null || allCategories.isEmpty()) {
            return result;
        }
        
        for (MaterialCategory category : allCategories) {
            // 找到指定父分类的直接子分类
            if (Objects.equals(category.getParentId(), parentId)) {
                // 递归构建子分类的子分类树
                List<MaterialCategory> children = buildCategoryTree(allCategories, category.getId());
                category.setChildren(children);
                result.add(category);
            }
        }
        
        // 按照编码排序
        result.sort((c1, c2) -> {
            if (c1.getCategoryCode() == null && c2.getCategoryCode() == null) {
                return 0;
            }
            if (c1.getCategoryCode() == null) {
                return 1;
            }
            if (c2.getCategoryCode() == null) {
                return -1;
            }
            return c1.getCategoryCode().compareTo(c2.getCategoryCode());
        });
        
        return result;
    }

    /**
     * 安全地将Object转换为int类型
     * 支持Integer、Long、BigInteger等数字类型
     */
    private int parseIntSafely(Object value) {
        if (value == null) {
            return 0;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Long) {
            return ((Long) value).intValue();
        } else if (value instanceof java.math.BigInteger) {
            return ((java.math.BigInteger) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法解析数字字符串: {}", value);
                return 0;
            }
        } else {
            log.warn("不支持的数字类型: {}, 类型: {}", value, value.getClass().getSimpleName());
            return 0;
        }
    }
}
