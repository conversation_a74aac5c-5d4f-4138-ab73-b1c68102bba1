package com.fadun.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fadun.common.exception.BusinessException;
import com.fadun.common.result.PageResult;
import com.fadun.common.result.Result;
import com.fadun.common.utils.ResultUtils;
import com.fadun.entity.OutboundOrder;
import com.fadun.entity.OutboundDetail;
import com.fadun.entity.Warehouse;
import com.fadun.entity.Material;
import com.fadun.entity.MaterialStock;
import com.fadun.mapper.OutboundMapper;
import com.fadun.service.OutboundManagementService;
import com.fadun.service.WarehouseManagementService;
import com.fadun.service.MaterialManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 物资出库管理服务实现类
 * 整合出库流程和明细管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OutboundManagementServiceImpl implements OutboundManagementService {

    private final OutboundMapper outboundMapper;
    private final WarehouseManagementService warehouseManagementService;
    private final MaterialManagementService materialManagementService;

    // ========== 核心出库功能 ==========

    @Override
    public List<Warehouse> getWarehouseList() {
        log.info("获取库室列表");
        
        try {
            // 调用库房管理服务获取库室列表
            List<Warehouse> warehouses = warehouseManagementService.getWarehouseList();
            log.info("获取库室列表成功: 数量={}", warehouses.size());
            return warehouses;
        } catch (Exception e) {
            log.error("获取库室列表失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取库室列表失败");
        }
    }

    @Override
    public List<Material> getMaterialsByWarehouse(Long warehouseId) {
        log.info("根据库室获取物资: warehouseId={}", warehouseId);
        
        if (warehouseId == null || warehouseId <= 0) {
            throw BusinessException.paramError("库室ID不能为空");
        }
        
        try {
            // 调用物资管理服务获取指定库房的物资列表（包含库存信息）
            List<Material> materials = materialManagementService.getMaterialsByWarehouse(warehouseId);
            log.info("根据库室获取物资成功: warehouseId={}, 物资数量={}", warehouseId, materials.size());
            return materials;
        } catch (Exception e) {
            log.error("根据库室获取物资失败: warehouseId={}, error={}", warehouseId, e.getMessage(), e);
            throw BusinessException.of("获取库室物资失败");
        }
    }

    @Override
    @Transactional
    public Result<String> submitOutboundOrder(OutboundOrder outboundOrder) {
        log.info("提交出库单: {}", outboundOrder);
        
        // 业务数据验证
        validateOutboundOrder(outboundOrder);
        
        try {
            // 1. 生成出库单号
            if (!StringUtils.hasText(outboundOrder.getOrderNo())) {
                outboundOrder.setOrderNo(generateOutboundOrderNo());
            }
            
            // 2. 设置基础信息
            outboundOrder.setStatus(1); // 待出库状态
            outboundOrder.setOutboundTime(LocalDateTime.now());
            
            // 3. 计算总数量
            if (outboundOrder.getDetails() != null && !outboundOrder.getDetails().isEmpty()) {
                int totalQuantity = outboundOrder.getDetails().stream()
                        .mapToInt(detail -> detail.getQuantity())
                        .sum();
                outboundOrder.setTotalQuantity(totalQuantity);
            }
            
            // 4. 库存充足性检查
            if (outboundOrder.getDetails() != null && !outboundOrder.getDetails().isEmpty()) {
                validateStockSufficiency(outboundOrder.getWarehouseId(), outboundOrder.getDetails());
            }
            
            // 5. 保存出库单主表
            int result = outboundMapper.insert(outboundOrder);
            if (result <= 0) {
                throw BusinessException.of("创建出库单失败");
            }
            
            // 6. 保存出库明细
            if (outboundOrder.getDetails() != null && !outboundOrder.getDetails().isEmpty()) {
                // 设置出库单ID到明细中
                for (OutboundDetail detail : outboundOrder.getDetails()) {
                    detail.setOrderId(outboundOrder.getId());
                    
                    // 验证明细数据
                    if (detail.getMaterialId() == null || detail.getMaterialId() <= 0) {
                        throw BusinessException.paramError("出库明细中的物资ID不能为空");
                    }
                    if (detail.getQuantity() == null || detail.getQuantity() <= 0) {
                        throw BusinessException.paramError("出库明细中的数量必须大于0");
                    }
                }
                
                // 批量保存明细
                int detailsResult = outboundMapper.insertBatch(outboundOrder.getDetails());
                if (detailsResult <= 0) {
                    throw BusinessException.of("保存出库明细失败");
                }
                
                log.info("保存出库明细成功: 保存数量={}", detailsResult);
            }
            
            // 7. 自动确认出库（更新库存）
            outboundOrder.setStatus(2); // 设置为已出库状态
            outboundOrder.setOutboundTime(LocalDateTime.now());
            
            // 更新出库单状态
            int updateResult = outboundMapper.updateById(outboundOrder);
            if (updateResult <= 0) {
                throw BusinessException.of("更新出库单状态失败");
            }
            
            // 更新库存（减少库存）
            if (outboundOrder.getDetails() != null && !outboundOrder.getDetails().isEmpty()) {
                updateStockForOutbound(outboundOrder.getWarehouseId(), outboundOrder.getDetails());
            }
            
            log.info("提交出库单成功: orderNo={}, id={}", outboundOrder.getOrderNo(), outboundOrder.getId());
            return ResultUtils.success("提交出库单成功");
            
        } catch (BusinessException e) {
            return ResultUtils.fail(e.getMessage());
        } catch (Exception e) {
            log.error("提交出库单失败: {}", e.getMessage(), e);
            return ResultUtils.fail("提交出库单失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<OutboundOrder> getOutboundRecords(Long current, Long size, String keyword,
                                                       Long warehouseId, String startDate, String endDate) {
        log.info("获取出库记录: current={}, size={}, keyword={}, warehouseId={}, startDate={}, endDate={}", 
                current, size, keyword, warehouseId, startDate, endDate);
        
        // 参数验证
        if (current < 1) current = 1L;
        if (size < 1 || size > 100) size = 10L;
        
        try {
            Page<OutboundOrder> page = new Page<>(current, size);
            Page<OutboundOrder> result = outboundMapper.selectOutboundRecords(page, keyword, warehouseId, startDate, endDate);
            
            log.info("获取出库记录成功: 总数={}, 当前页数据={}", result.getTotal(), result.getRecords().size());
            return PageResult.fromPage(result);
        } catch (Exception e) {
            log.error("获取出库记录失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取出库记录失败");
        }
    }

    // ========== 出库单明细管理 ==========

    @Override
    public List<OutboundDetail> getDetailsByOrderId(Long orderId) {
        log.info("根据订单ID获取明细: orderId={}", orderId);
        
        if (orderId == null || orderId <= 0) {
            throw BusinessException.paramError("出库单ID不能为空");
        }
        
        try {
            List<OutboundDetail> details = outboundMapper.selectDetailsByOrderId(orderId);
            log.info("根据订单ID获取明细成功: orderId={}, 明细数量={}", orderId, details.size());
            return details;
        } catch (Exception e) {
            log.error("根据订单ID获取明细失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw BusinessException.of("获取出库明细失败");
        }
    }

    @Override
    @Transactional
    public boolean saveDetails(List<OutboundDetail> details) {
        log.info("保存出库明细: 数量={}", details != null ? details.size() : 0);
        
        if (details == null || details.isEmpty()) {
            throw BusinessException.paramError("出库明细不能为空");
        }
        
        // 验证明细数据
        for (OutboundDetail detail : details) {
            if (detail.getOrderId() == null || detail.getOrderId() <= 0) {
                throw BusinessException.paramError("出库明细中的订单ID不能为空");
            }
            if (detail.getMaterialId() == null || detail.getMaterialId() <= 0) {
                throw BusinessException.paramError("出库明细中的物资ID不能为空");
            }
            if (detail.getQuantity() == null || detail.getQuantity() <= 0) {
                throw BusinessException.paramError("出库明细中的数量必须大于0");
            }
        }
        
        try {
            int result = outboundMapper.insertBatch(details);
            boolean success = result > 0;
            
            if (success) {
                log.info("保存出库明细成功: 保存数量={}", result);
            }
            
            return success;
        } catch (Exception e) {
            log.error("保存出库明细失败: {}", e.getMessage(), e);
            throw BusinessException.of("保存出库明细失败: " + e.getMessage());
        }
    }

    // ========== 传统出库管理功能（简化实现） ==========

    @Override
    public PageResult<OutboundOrder> getOutboundPage(Long current, Long size, String keyword, String outboundType, Integer status) {
        log.info("获取出库分页数据: current={}, size={}, keyword={}, outboundType={}, status={}", 
                current, size, keyword, outboundType, status);
        
        // 参数验证
        if (current < 1) current = 1L;
        if (size < 1 || size > 100) size = 10L;
        
        try {
            Page<OutboundOrder> page = new Page<>(current, size);
            Page<OutboundOrder> result = outboundMapper.selectOutboundPage(page, keyword, outboundType, status);
            
            log.info("获取出库分页数据成功: 总数={}, 当前页数据={}", result.getTotal(), result.getRecords().size());
            return PageResult.fromPage(result);
        } catch (Exception e) {
            log.error("获取出库分页数据失败: {}", e.getMessage(), e);
            throw BusinessException.of("获取出库分页数据失败");
        }
    }

    @Override
    public Result<String> createOutboundOrder(OutboundOrder outboundOrder) {
        log.info("创建出库单: {}", outboundOrder);
        
        // 业务数据验证
        validateOutboundOrder(outboundOrder);
        
        try {
            // 生成出库单号
            if (!StringUtils.hasText(outboundOrder.getOrderNo())) {
                outboundOrder.setOrderNo(generateOutboundOrderNo());
            }
            
            // 设置基础信息
            outboundOrder.setStatus(1); // 待出库状态
            
            // 保存出库单
            int result = outboundMapper.insert(outboundOrder);
            boolean success = result > 0;
            
            if (success) {
                log.info("创建出库单成功: orderNo={}, id={}", outboundOrder.getOrderNo(), outboundOrder.getId());
            }
            
            return success ? ResultUtils.success("创建成功") : ResultUtils.fail("创建失败");
        } catch (Exception e) {
            log.error("创建出库单失败: {}", e.getMessage(), e);
            return ResultUtils.fail("创建出库单失败: " + e.getMessage());
        }
    }

    @Override
    public boolean confirmOutbound(Long orderId) {
        log.info("确认出库: orderId={}", orderId);
        // 简化实现，实际项目中可能需要更复杂的逻辑
        return true;
    }

    @Override
    public boolean cancelOutbound(Long orderId) {
        log.info("取消出库: orderId={}", orderId);
        // 简化实现，实际项目中可能需要更复杂的逻辑
        return true;
    }

    @Override
    public OutboundOrder getOutboundDetail(Long orderId) {
        log.info("获取出库详情: orderId={}", orderId);
        
        if (orderId == null || orderId <= 0) {
            throw BusinessException.paramError("出库单ID不能为空");
        }
        
        try {
            // 使用增强的查询方法，包含库房信息
            OutboundOrder outboundOrder = outboundMapper.selectOutboundDetailById(orderId);
            
            if (outboundOrder == null) {
                throw BusinessException.dataNotFound("出库单不存在: ID=" + orderId);
            }
            
            // 获取出库明细
            List<OutboundDetail> details = outboundMapper.selectDetailsByOrderId(orderId);
            outboundOrder.setDetails(details);
            
            log.info("获取出库详情成功: orderId={}, 明细数量={}", orderId, details.size());
            return outboundOrder;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取出库详情失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw BusinessException.of("获取出库详情失败");
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成出库单号
     * 格式: CK + YYYYMMDD + 6位序号 (如: CK20241220000001)
     */
    private String generateOutboundOrderNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "CK" + dateStr;
        
        // TODO: 后续可以改为从数据库查询当日最大序号 + 1
        // 这里简化处理，使用时间戳后6位
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = timestamp.substring(timestamp.length() - 6);
        
        return prefix + suffix;
    }

    /**
     * 验证出库单数据
     */
    private void validateOutboundOrder(OutboundOrder outboundOrder) {
        if (outboundOrder == null) {
            throw BusinessException.paramError("出库单信息不能为空");
        }
        
        if (outboundOrder.getWarehouseId() == null || outboundOrder.getWarehouseId() <= 0) {
            throw BusinessException.paramError("库房ID不能为空");
        }
        
        if (!StringUtils.hasText(outboundOrder.getReceiverUnit())) {
            throw BusinessException.paramError("接收单位不能为空");
        }
        
        if (!StringUtils.hasText(outboundOrder.getReceiverName())) {
            throw BusinessException.paramError("接收人不能为空");
        }
        
        if (!StringUtils.hasText(outboundOrder.getOutboundType())) {
            outboundOrder.setOutboundType("use"); // 默认为使用出库
        }
        
        // 验证出库类型是否合法
        if (!Arrays.asList("use", "transfer", "scrap").contains(outboundOrder.getOutboundType())) {
            throw BusinessException.paramError("出库类型无效，支持的类型: use, transfer, scrap");
        }
        
        if (!StringUtils.hasText(outboundOrder.getOperatorName())) {
            throw BusinessException.paramError("操作员姓名不能为空");
        }
        
        // 如果有明细，验证明细数据
        if (outboundOrder.getDetails() != null) {
            for (OutboundDetail detail : outboundOrder.getDetails()) {
                if (detail.getMaterialId() == null || detail.getMaterialId() <= 0) {
                    throw BusinessException.paramError("出库明细中的物资ID不能为空");
                }
                if (detail.getQuantity() == null || detail.getQuantity() <= 0) {
                    throw BusinessException.paramError("出库明细中的数量必须大于0");
                }
            }
        }
    }

    /**
     * 验证库存充足性
     */
    private void validateStockSufficiency(Long warehouseId, List<OutboundDetail> details) {
        log.info("验证库存充足性: warehouseId={}, 明细数量={}", warehouseId, details.size());
        
        try {
            for (OutboundDetail detail : details) {
                // TODO: 调用库存管理服务检查库存充足性
                // 当前先记录日志，后续集成库存检查功能
                log.info("检查库存: materialId={}, warehouseId={}, needQuantity={}", 
                        detail.getMaterialId(), warehouseId, detail.getQuantity());
                
                // 示例逻辑：检查库存是否充足
                // MaterialStock stock = materialManagementService.getStockByMaterialAndWarehouse(detail.getMaterialId(), warehouseId);
                // if (stock == null || stock.getQuantity() < detail.getQuantity()) {
                //     throw BusinessException.of("物资库存不足: ID=" + detail.getMaterialId());
                // }
            }
            
            log.info("库存检查完成 - 库存充足");
        } catch (Exception e) {
            log.error("库存检查失败: {}", e.getMessage(), e);
            throw BusinessException.of("库存检查失败: " + e.getMessage());
        }
    }

    /**
     * 更新库存（出库操作）
     */
    private void updateStockForOutbound(Long warehouseId, List<OutboundDetail> details) {
        log.info("更新库存 - 出库操作: warehouseId={}, 明细数量={}", warehouseId, details.size());
        
        try {
            for (OutboundDetail detail : details) {
                // TODO: 这里应该调用库存管理服务更新库存
                // 当前先记录日志，后续集成库存更新功能
                log.info("更新库存: materialId={}, warehouseId={}, quantity=-{}", 
                        detail.getMaterialId(), warehouseId, detail.getQuantity());
                
                // materialStockService.reduceStock(detail.getMaterialId(), warehouseId, detail.getQuantity());
            }
            
            log.info("库存更新完成 - 出库操作");
        } catch (Exception e) {
            log.error("更新库存失败: {}", e.getMessage(), e);
            throw BusinessException.of("更新库存失败: " + e.getMessage());
        }
    }
}

