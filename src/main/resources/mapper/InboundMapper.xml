<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.InboundMapper">

    <!-- 分页查询入库单 -->
    <select id="selectInboundPage" resultType="com.fadun.entity.InboundOrder">
        SELECT 
            io.id,
            io.order_no,
            io.warehouse_id,
            io.supplier,
            io.inbound_type,
            io.total_quantity,
            io.operator_id,
            io.operator_name,
            io.inbound_time,
            io.status,
            io.remark,
            io.create_time,
            io.update_time,
            io.create_by,
            io.update_by,
            w.warehouse_name,
            CASE 
                WHEN io.status = 1 THEN '待入库'
                WHEN io.status = 2 THEN '已入库'
                WHEN io.status = 3 THEN '已取消'
                ELSE '未知'
            END as statusName
        FROM inbound_order io
        LEFT JOIN warehouse w ON io.warehouse_id = w.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (io.order_no LIKE CONCAT('%', #{keyword}, '%') 
                     OR io.supplier LIKE CONCAT('%', #{keyword}, '%')
                     OR io.operator_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="inboundType != null and inboundType != ''">
                AND io.inbound_type = #{inboundType}
            </if>
            <if test="status != null">
                AND io.status = #{status}
            </if>
        </where>
        ORDER BY io.create_time DESC, io.id DESC
    </select>

    <!-- 根据条件查询入库记录（支持日期范围） -->
    <select id="selectInboundRecords" resultType="com.fadun.entity.InboundOrder">
        SELECT 
            io.id,
            io.order_no,
            io.warehouse_id,
            io.supplier,
            io.inbound_type,
            io.total_quantity,
            io.operator_id,
            io.operator_name,
            io.inbound_time,
            io.status,
            io.remark,
            io.create_time,
            io.update_time,
            w.warehouse_name,
            CASE 
                WHEN io.status = 1 THEN '待入库'
                WHEN io.status = 2 THEN '已入库'
                WHEN io.status = 3 THEN '已取消'
                ELSE '未知'
            END as statusName
        FROM inbound_order io
        LEFT JOIN warehouse w ON io.warehouse_id = w.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (io.order_no LIKE CONCAT('%', #{keyword}, '%') 
                     OR io.supplier LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="warehouseId != null">
                AND io.warehouse_id = #{warehouseId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE(io.inbound_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(io.inbound_time) &lt;= #{endDate}
            </if>
            AND io.status = 2
        </where>
        ORDER BY io.inbound_time DESC, io.id DESC
    </select>

    <!-- 根据ID查询入库单详情（包含明细） -->
    <select id="selectInboundDetailById" resultType="com.fadun.entity.InboundOrder">
        SELECT 
            io.id,
            io.order_no,
            io.warehouse_id,
            io.supplier,
            io.inbound_type,
            io.total_quantity,
            io.operator_id,
            io.operator_name,
            io.inbound_time,
            io.status,
            io.remark,
            io.create_time,
            io.update_time,
            io.create_by,
            io.update_by,
            w.warehouse_name,
            CASE 
                WHEN io.status = 1 THEN '待入库'
                WHEN io.status = 2 THEN '已入库'
                WHEN io.status = 3 THEN '已取消'
                ELSE '未知'
            END as statusName
        FROM inbound_order io
        LEFT JOIN warehouse w ON io.warehouse_id = w.id
        WHERE io.id = #{id}
    </select>

    <!-- 批量插入入库明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO inbound_detail (order_id, material_id, quantity, remark)
        VALUES
        <foreach collection="details" item="detail" separator=",">
            (#{detail.orderId}, #{detail.materialId}, #{detail.quantity}, #{detail.remark})
        </foreach>
    </insert>

    <!-- 统计入库概览信息 -->
    <select id="getInboundStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalInboundOrders,
            COUNT(CASE WHEN status = 1 THEN 1 END) as pendingInboundOrders,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completedInboundOrders,
            COUNT(CASE WHEN status = 3 THEN 1 END) as cancelledInboundOrders,
            COALESCE(SUM(CASE WHEN status = 2 THEN total_quantity ELSE 0 END), 0) as totalInboundQuantity,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayInboundOrders,
            COUNT(CASE WHEN DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as weeklyInboundOrders,
            COUNT(CASE WHEN YEAR(create_time) = YEAR(CURDATE()) AND MONTH(create_time) = MONTH(CURDATE()) THEN 1 END) as monthlyInboundOrders
        FROM inbound_order
    </select>

    <!-- 按月统计入库数量 -->
    <select id="getMonthlyInboundStats" resultType="map">
        SELECT 
            DATE_FORMAT(inbound_time, '%Y-%m') as month,
            COUNT(*) as orderCount,
            SUM(total_quantity) as totalQuantity
        FROM inbound_order 
        WHERE status = 2 
          AND inbound_time &gt;= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(inbound_time, '%Y-%m')
        ORDER BY month DESC
    </select>

    <!-- 按入库类型统计 -->
    <select id="getInboundTypeStats" resultType="map">
        SELECT 
            inbound_type,
            COUNT(*) as orderCount,
            SUM(total_quantity) as totalQuantity,
            CASE 
                WHEN inbound_type = 'purchase' THEN '采购入库'
                WHEN inbound_type = 'return' THEN '退货入库'
                WHEN inbound_type = 'transfer' THEN '调拨入库'
                ELSE '其他入库'
            END as inboundTypeName
        FROM inbound_order 
        WHERE status = 2
        GROUP BY inbound_type
        ORDER BY orderCount DESC
    </select>

</mapper>