<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.InventoryMapper">

    <!-- ========== 盘点单管理 ========== -->

    <!-- 分页查询盘点单 -->
    <select id="selectInventoryOrderPage" resultType="com.fadun.entity.InventoryOrder">
        SELECT 
            io.*,
            w.warehouse_name
        FROM inventory_order io
        LEFT JOIN warehouse w ON io.warehouse_id = w.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (io.order_no LIKE CONCAT('%', #{keyword}, '%') 
                     OR io.operator_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="warehouseId != null">
                AND io.warehouse_id = #{warehouseId}
            </if>
            <if test="inventoryType != null and inventoryType != ''">
                AND io.inventory_type = #{inventoryType}
            </if>
            <if test="status != null">
                AND io.status = #{status}
            </if>
        </where>
        ORDER BY io.create_time DESC
    </select>

    <!-- 查询盘点单详情 -->
    <select id="selectInventoryOrderDetail" resultType="com.fadun.entity.InventoryOrder">
        SELECT 
            io.*,
            w.warehouse_name
        FROM inventory_order io
        LEFT JOIN warehouse w ON io.warehouse_id = w.id
        WHERE io.id = #{orderId}
    </select>

    <!-- ========== 盘点明细管理 ========== -->

    <!-- 根据盘点单ID查询明细列表 -->
    <select id="selectInventoryDetailsByOrderId" resultType="com.fadun.entity.InventoryDetail">
        SELECT 
            id.*,
            m.material_code,
            m.material_name,
            m.specification,
            m.unit,
            mc.category_name
        FROM inventory_detail id
        LEFT JOIN material m ON id.material_id = m.id
        LEFT JOIN material_category mc ON m.category_id = mc.id
        WHERE id.order_id = #{orderId}
        ORDER BY id.id ASC
    </select>

    <!-- 批量插入盘点明细 -->
    <insert id="insertInventoryDetailBatch" parameterType="java.util.List">
        INSERT INTO inventory_detail (
            order_id, material_id, book_quantity, actual_quantity, 
            difference_quantity, location, status, remark
        ) VALUES
        <foreach collection="list" item="detail" separator=",">
            (
                #{detail.orderId}, #{detail.materialId}, #{detail.bookQuantity}, 
                #{detail.actualQuantity}, #{detail.differenceQuantity}, 
                #{detail.location}, #{detail.status}, #{detail.remark}
            )
        </foreach>
    </insert>

    <!-- 更新盘点明细 -->
    <update id="updateInventoryDetail" parameterType="com.fadun.entity.InventoryDetail">
        UPDATE inventory_detail 
        SET 
            actual_quantity = #{actualQuantity},
            difference_quantity = #{differenceQuantity},
            status = #{status},
            remark = #{remark}
        WHERE id = #{id}
    </update>

    <!-- ========== 库存数据查询 ========== -->

    <!-- 根据盘点单和物资ID查询库存 -->
    <select id="selectStockByOrderIdAndMaterialIds" resultType="com.fadun.entity.MaterialStock">
        SELECT 
            ms.*,
            m.material_code,
            m.material_name,
            m.specification,
            m.unit
        FROM material_stock ms
        LEFT JOIN material m ON ms.material_id = m.id
        WHERE ms.warehouse_id = (
            SELECT warehouse_id 
            FROM inventory_order 
            WHERE id = #{orderId}
        )
        <if test="materialIds != null and materialIds.size() > 0">
            AND ms.material_id IN
            <foreach collection="materialIds" item="materialId" open="(" separator="," close=")">
                #{materialId}
            </foreach>
        </if>
        ORDER BY ms.material_id ASC
    </select>

    <!-- 查询库存统计信息（用于盘点分析） -->
    <select id="selectStockStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalMaterials,
            SUM(CASE WHEN current_stock > 0 THEN 1 ELSE 0 END) as inStockMaterials,
            SUM(CASE WHEN current_stock = 0 THEN 1 ELSE 0 END) as outOfStockMaterials,
            SUM(current_stock) as totalQuantity
        FROM material_stock ms
        WHERE ms.warehouse_id = #{warehouseId}
    </select>

    <!-- ========== 统计查询 ========== -->

    <!-- 获取盘点统计 -->
    <select id="getInventoryStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalOrders,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as inProgressOrders,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completedOrders,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as cancelledOrders,
            SUM(total_items) as totalItems,
            SUM(diff_items) as totalDiffItems
        FROM inventory_order
        WHERE DATE(create_time) = CURDATE()
    </select>

    <!-- 获取月度盘点统计 -->
    <select id="getMonthlyInventoryStats" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(create_time, '%Y-%m') as month,
            COUNT(*) as orderCount,
            SUM(total_items) as totalItems,
            SUM(diff_items) as diffItems
        FROM inventory_order
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(create_time, '%Y-%m')
        ORDER BY month DESC
    </select>

    <!-- 获取盘点类型统计 -->
    <select id="getInventoryTypeStats" resultType="java.util.Map">
        SELECT 
            inventory_type,
            COUNT(*) as count,
            SUM(total_items) as totalItems,
            AVG(diff_items) as avgDiffItems
        FROM inventory_order
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
        GROUP BY inventory_type
    </select>

</mapper>
