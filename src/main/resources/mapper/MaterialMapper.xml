<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.MaterialMapper">

    <!-- 分页查询物资台账 -->
    <select id="selectMaterialPage" resultType="com.fadun.entity.Material">
        SELECT 
            m.id,
            m.material_code,
            m.material_name,
            m.category_id,
            m.specification,
            m.unit,
            m.brand,
            m.model,
            m.status,
            m.remark,
            m.create_time,
            m.update_time,
            m.create_by,
            m.update_by,
            c.category_name
        FROM material m
        LEFT JOIN material_category c ON m.category_id = c.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (m.material_code LIKE CONCAT('%', #{keyword}, '%') 
                     OR m.material_name LIKE CONCAT('%', #{keyword}, '%')
                     OR m.specification LIKE CONCAT('%', #{keyword}, '%')
                     OR m.brand LIKE CONCAT('%', #{keyword}, '%')
                     OR m.model LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="categoryId != null">
                AND m.category_id = #{categoryId}
            </if>
            <if test="warehouseId != null">
                AND EXISTS (
                    SELECT 1 FROM material_stock s 
                    WHERE s.material_id = m.id 
                    AND s.warehouse_id = #{warehouseId}
                )
            </if>
            AND m.status = 1
        </where>
        ORDER BY m.create_time DESC
    </select>

    <!-- 根据库室ID查询物资列表 -->
    <select id="selectMaterialsByWarehouse" resultType="com.fadun.entity.Material">
        SELECT DISTINCT
            m.id,
            m.material_code,
            m.material_name,
            m.category_id,
            m.specification,
            m.unit,
            m.brand,
            m.model,
            m.status,
            m.remark,
            c.category_name
        FROM material m
        LEFT JOIN material_category c ON m.category_id = c.id
        INNER JOIN material_stock ms ON m.id = ms.material_id
        WHERE ms.warehouse_id = #{warehouseId} 
          AND m.status = 1
          AND ms.current_stock > 0
        ORDER BY m.material_name
    </select>

    <!-- 查询物资详情（包含分类信息和库存汇总） -->
    <select id="selectMaterialDetailById" resultType="com.fadun.entity.Material">
        SELECT 
            m.id,
            m.material_code,
            m.material_name,
            m.category_id,
            m.specification,
            m.unit,
            m.brand,
            m.model,
            m.status,
            m.remark,
            m.create_time,
            m.update_time,
            m.create_by,
            m.update_by,
            c.category_name,
            COALESCE(SUM(ms.current_stock), 0) as totalStock
        FROM material m
        LEFT JOIN material_category c ON m.category_id = c.id
        LEFT JOIN material_stock ms ON m.id = ms.material_id
        WHERE m.id = #{id}
        GROUP BY m.id, c.category_name
    </select>

    <!-- 检查物资编码是否存在 -->
    <select id="countByMaterialCode" resultType="int">
        SELECT COUNT(1) 
        FROM material 
        WHERE material_code = #{materialCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 统计物资总数量 -->
    <select id="countTotalMaterials" resultType="int">
        SELECT COUNT(*) FROM material WHERE status = 1
    </select>

    <!-- 统计各分类物资数量 -->
    <select id="countMaterialsByCategory" resultType="map">
        SELECT 
            c.id as categoryId,
            c.category_name as categoryName,
            COUNT(m.id) as materialCount
        FROM material_category c
        LEFT JOIN material m ON c.id = m.category_id AND m.status = 1
        GROUP BY c.id, c.category_name
        ORDER BY materialCount DESC
    </select>

    <!-- 统计各状态物资数量 -->
    <select id="countMaterialsByStatus" resultType="map">
        SELECT 
            CASE 
                WHEN status = 1 THEN '正常'
                WHEN status = 0 THEN '停用'
                ELSE '未知'
            END as statusName,
            status,
            COUNT(*) as materialCount
        FROM material 
        GROUP BY status
        ORDER BY status DESC
    </select>

    <!-- 获取物资统计概览 -->
    <select id="getMaterialStatisticsOverview" resultType="map">
        SELECT 
            COUNT(*) as totalMaterials,
            COUNT(CASE WHEN status = 1 THEN 1 END) as activeMaterials,
            COUNT(CASE WHEN status = 0 THEN 1 END) as inactiveMaterials,
            COUNT(DISTINCT category_id) as totalCategories,
            COUNT(CASE WHEN create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recentMaterials
        FROM material
    </select>

    <!-- 查询库存不足的物资 -->
    <select id="selectLowStockMaterials" resultType="map">
        SELECT 
            m.id,
            m.material_code,
            m.material_name,
            m.specification,
            m.unit,
            c.category_name,
            SUM(ms.current_stock) as totalStock
        FROM material m
        LEFT JOIN material_category c ON m.category_id = c.id
        LEFT JOIN material_stock ms ON m.id = ms.material_id
        WHERE m.status = 1
        GROUP BY m.id, m.material_code, m.material_name, m.specification, m.unit, c.category_name
        HAVING totalStock &lt; 10  -- 假设低于10为库存不足
        ORDER BY totalStock ASC
        LIMIT 20
    </select>

    <!-- ==================== 物资分类相关查询 ==================== -->

    <!-- 查询分类树形结构 -->
    <select id="selectCategoryTree" resultType="com.fadun.entity.MaterialCategory">
        SELECT 
            id,
            category_code,
            category_name,
            parent_id,
            level,
            remark
        FROM material_category
        <where>
            <if test="parentId != null">
                parent_id = #{parentId}
            </if>
            <if test="parentId == null">
                parent_id = 0 OR parent_id IS NULL
            </if>
        </where>
        ORDER BY category_code ASC, id ASC
    </select>

    <!-- 查询分类及其物资数量 -->
    <select id="selectCategoryWithMaterialCount" resultType="com.fadun.entity.MaterialCategory">
        SELECT 
            c.id,
            c.category_code,
            c.category_name,
            c.parent_id,
            c.level,
            c.remark,
            COALESCE(COUNT(m.id), 0) as materialCount,
            p.category_name as parentName
        FROM material_category c
        LEFT JOIN material_category p ON c.parent_id = p.id
        LEFT JOIN material m ON c.id = m.category_id AND m.status = 1
        GROUP BY c.id, c.category_code, c.category_name, c.parent_id, c.level, c.remark, p.category_name
        ORDER BY c.level ASC, c.category_code ASC
    </select>

    <!-- 检查分类下是否有物资 -->
    <select id="countMaterialsInCategory" resultType="int">
        SELECT COUNT(*)
        FROM material 
        WHERE category_id = #{categoryId} AND status = 1
    </select>

    <!-- 检查分类下是否有子分类 -->
    <select id="countChildrenByCategory" resultType="int">
        SELECT COUNT(*)
        FROM material_category 
        WHERE parent_id = #{categoryId}
    </select>

    <!-- 检查分类编码是否存在 -->
    <select id="countByCategoryCode" resultType="int">
        SELECT COUNT(1)
        FROM material_category 
        WHERE category_code = #{categoryCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据ID查询分类详情 -->
    <select id="selectCategoryById" resultType="com.fadun.entity.MaterialCategory">
        SELECT 
            c.id,
            c.category_code,
            c.category_name,
            c.parent_id,
            c.level,
            c.remark,
            p.category_name as parentName,
            COALESCE(COUNT(m.id), 0) as materialCount
        FROM material_category c
        LEFT JOIN material_category p ON c.parent_id = p.id
        LEFT JOIN material m ON c.id = m.category_id AND m.status = 1
        WHERE c.id = #{id}
        GROUP BY c.id, c.category_code, c.category_name, c.parent_id, c.level, c.remark, p.category_name
    </select>

    <!-- 查询所有分类列表 -->
    <select id="selectAllCategories" resultType="com.fadun.entity.MaterialCategory">
        SELECT 
            c.id,
            c.category_code,
            c.category_name,
            c.parent_id,
            c.level,
            c.remark,
            p.category_name as parentName
        FROM material_category c
        LEFT JOIN material_category p ON c.parent_id = p.id
        ORDER BY c.level ASC, c.category_code ASC
    </select>

    <!-- 根据父级ID查询子分类 -->
    <select id="selectCategoriesByParentId" resultType="com.fadun.entity.MaterialCategory">
        SELECT 
            id,
            category_code,
            category_name,
            parent_id,
            level,
            remark
        FROM material_category
        WHERE parent_id = #{parentId}
        ORDER BY category_code ASC, id ASC
    </select>

    <!-- 插入分类 -->
    <insert id="insertCategory" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO material_category (
            category_code,
            category_name,
            parent_id,
            level,
            remark
        ) VALUES (
            #{categoryCode},
            #{categoryName},
            #{parentId},
            #{level},
            #{remark}
        )
    </insert>

    <!-- 更新分类 -->
    <update id="updateCategoryById">
        UPDATE material_category 
        SET 
            category_code = #{categoryCode},
            category_name = #{categoryName},
            parent_id = #{parentId},
            level = #{level},
            remark = #{remark}
        WHERE id = #{id}
    </update>

    <!-- 删除分类 -->
    <delete id="deleteCategoryById">
        DELETE FROM material_category WHERE id = #{id}
    </delete>

    <!-- ==================== 库存查询相关SQL ==================== -->

    <!-- 分页查询库存信息 -->
    <select id="selectStockPage" resultType="com.fadun.entity.MaterialStock">
        SELECT 
            ms.id,
            ms.material_id,
            ms.warehouse_id,
            ms.current_stock,
            ms.location,
            ms.last_in_time,
            ms.last_out_time,
            ms.update_time,
            m.material_code,
            m.material_name,
            m.specification,
            m.unit,
            w.warehouse_name,
            c.category_name,
            m.category_id
        FROM material_stock ms
        LEFT JOIN material m ON ms.material_id = m.id
        LEFT JOIN warehouse w ON ms.warehouse_id = w.id
        LEFT JOIN material_category c ON m.category_id = c.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (m.material_code LIKE CONCAT('%', #{keyword}, '%') 
                     OR m.material_name LIKE CONCAT('%', #{keyword}, '%')
                     OR m.specification LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="materialId != null">
                AND ms.material_id = #{materialId}
            </if>
            <if test="warehouseId != null">
                AND ms.warehouse_id = #{warehouseId}
            </if>
            <if test="categoryId != null">
                AND m.category_id = #{categoryId}
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                <choose>
                    <when test="stockStatus == 'zero'">
                        AND ms.current_stock = 0
                    </when>
                    <when test="stockStatus == 'low'">
                        AND ms.current_stock > 0 AND ms.current_stock &lt; 10
                    </when>
                    <when test="stockStatus == 'normal'">
                        AND ms.current_stock >= 10
                    </when>
                </choose>
            </if>
            AND m.status = 1
        </where>
        ORDER BY ms.update_time DESC, m.material_code ASC
    </select>

    <!-- 查询物资的总库存信息 -->
    <select id="selectMaterialWithTotalStock" resultType="com.fadun.entity.MaterialStock">
        SELECT 
            m.id as material_id,
            m.material_code,
            m.material_name,
            m.specification,
            m.unit,
            c.category_name,
            m.category_id,
            COALESCE(SUM(ms.current_stock), 0) as totalStock
        FROM material m
        LEFT JOIN material_category c ON m.category_id = c.id
        LEFT JOIN material_stock ms ON m.id = ms.material_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (m.material_code LIKE CONCAT('%', #{keyword}, '%') 
                     OR m.material_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="categoryId != null">
                AND m.category_id = #{categoryId}
            </if>
            AND m.status = 1
        </where>
        GROUP BY m.id, m.material_code, m.material_name, m.specification, m.unit, c.category_name, m.category_id
        ORDER BY totalStock DESC, m.material_code ASC
    </select>

    <!-- 检查指定物资在指定库房的库存数量 -->
    <select id="selectStockByMaterialAndWarehouse" resultType="com.fadun.entity.MaterialStock">
        SELECT 
            ms.id,
            ms.material_id,
            ms.warehouse_id,
            ms.current_stock,
            ms.location,
            ms.last_in_time,
            ms.last_out_time,
            ms.update_time,
            m.material_code,
            m.material_name,
            w.warehouse_name
        FROM material_stock ms
        LEFT JOIN material m ON ms.material_id = m.id
        LEFT JOIN warehouse w ON ms.warehouse_id = w.id
        WHERE ms.material_id = #{materialId} 
          AND ms.warehouse_id = #{warehouseId}
    </select>

    <!-- 统计库存概览信息 -->
    <select id="getStockOverview" resultType="map">
        SELECT 
            COUNT(DISTINCT ms.material_id) as totalMaterials,
            COUNT(ms.id) as totalStockRecords,
            SUM(ms.current_stock) as totalStock,
            COUNT(CASE WHEN ms.current_stock = 0 THEN 1 END) as zeroStockCount,
            COUNT(CASE WHEN ms.current_stock > 0 AND ms.current_stock &lt; 10 THEN 1 END) as lowStockCount,
            COUNT(CASE WHEN ms.current_stock >= 10 THEN 1 END) as normalStockCount,
            AVG(ms.current_stock) as avgStock
        FROM material_stock ms
        LEFT JOIN material m ON ms.material_id = m.id
        <where>
            <if test="warehouseId != null">
                AND ms.warehouse_id = #{warehouseId}
            </if>
            AND m.status = 1
        </where>
    </select>

    <!-- 查询库存预警信息 -->
    <select id="selectStockWarnings" resultType="map">
        SELECT 
            m.id as materialId,
            m.material_code as materialCode,
            m.material_name as materialName,
            m.specification,
            m.unit,
            w.id as warehouseId,
            w.warehouse_name as warehouseName,
            ms.current_stock as currentStock,
            ms.location,
            CASE 
                WHEN ms.current_stock = 0 THEN '零库存'
                WHEN ms.current_stock &lt; #{lowStockThreshold} THEN '低库存'
                ELSE '正常'
            END as stockStatusDesc
        FROM material_stock ms
        LEFT JOIN material m ON ms.material_id = m.id
        LEFT JOIN warehouse w ON ms.warehouse_id = w.id
        WHERE ms.current_stock &lt; #{lowStockThreshold}
          AND m.status = 1
        ORDER BY ms.current_stock ASC, m.material_code ASC
        LIMIT 50
    </select>

    <!-- 根据库房ID统计各状态库存数量 -->
    <select id="countStockByStatus" resultType="map">
        SELECT 
            CASE 
                WHEN ms.current_stock = 0 THEN '零库存'
                WHEN ms.current_stock > 0 AND ms.current_stock &lt; 10 THEN '低库存'
                WHEN ms.current_stock >= 10 AND ms.current_stock &lt; 50 THEN '正常库存'
                WHEN ms.current_stock >= 50 THEN '充足库存'
                ELSE '未知'
            END as stockStatus,
            COUNT(*) as stockCount,
            SUM(ms.current_stock) as totalStock
        FROM material_stock ms
        LEFT JOIN material m ON ms.material_id = m.id
        <where>
            <if test="warehouseId != null">
                AND ms.warehouse_id = #{warehouseId}
            </if>
            AND m.status = 1
        </where>
        GROUP BY 
            CASE 
                WHEN ms.current_stock = 0 THEN '零库存'
                WHEN ms.current_stock > 0 AND ms.current_stock &lt; 10 THEN '低库存'
                WHEN ms.current_stock >= 10 AND ms.current_stock &lt; 50 THEN '正常库存'
                WHEN ms.current_stock >= 50 THEN '充足库存'
                ELSE '未知'
            END
        ORDER BY 
            CASE 
                WHEN ms.current_stock = 0 THEN 1
                WHEN ms.current_stock > 0 AND ms.current_stock &lt; 10 THEN 2
                WHEN ms.current_stock >= 10 AND ms.current_stock &lt; 50 THEN 3
                WHEN ms.current_stock >= 50 THEN 4
                ELSE 5
            END
    </select>

</mapper>
