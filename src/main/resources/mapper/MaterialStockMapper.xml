<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.MaterialStockMapper">

    <!-- 分页查询库存信息 -->
    <select id="selectStockPage" resultType="com.fadun.entity.MaterialStock">
        SELECT 
            s.id,
            s.material_id,
            s.warehouse_id,
            s.current_stock,
            s.location,
            s.last_in_time,
            s.last_out_time,
            s.update_time,
            m.material_code,
            m.material_name,
            m.specification,
            m.unit,
            m.category_id,
            w.warehouse_name,
            c.category_name
        FROM material_stock s
        LEFT JOIN material m ON s.material_id = m.id
        LEFT JOIN warehouse w ON s.warehouse_id = w.id
        LEFT JOIN material_category c ON m.category_id = c.id
        <where>
            <if test="materialId != null">
                AND s.material_id = #{materialId}
            </if>
            <if test="warehouseId != null">
                AND s.warehouse_id = #{warehouseId}
            </if>
            <if test="categoryId != null">
                AND m.category_id = #{categoryId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (m.material_code LIKE CONCAT('%', #{keyword}, '%') 
                     OR m.material_name LIKE CONCAT('%', #{keyword}, '%')
                     OR w.warehouse_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="stockStatus != null and stockStatus != ''">
                <choose>
                    <when test="stockStatus == 'zero'">
                        AND s.current_stock = 0
                    </when>
                    <when test="stockStatus == 'low'">
                        AND s.current_stock > 0 AND s.current_stock &lt;= 10
                    </when>
                    <when test="stockStatus == 'normal'">
                        AND s.current_stock > 10
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY s.update_time DESC
    </select>

    <!-- 获取库存统计信息 -->
    <select id="selectStockStatistics" resultType="com.fadun.entity.MaterialStock">
        SELECT
            s.material_id,
            m.material_code,
            m.material_name,
            m.unit,
            SUM(s.current_stock) as totalStock
        FROM material_stock s
        LEFT JOIN material m ON s.material_id = m.id
        <where>
            <if test="warehouseId != null">
                AND s.warehouse_id = #{warehouseId}
            </if>
        </where>
        GROUP BY s.material_id, m.material_code, m.material_name, m.unit
        ORDER BY m.material_code
    </select>

    <!-- 更新库存数量 -->
    <update id="updateStock">
        UPDATE material_stock 
        SET current_stock = current_stock + #{quantity},
            update_time = NOW()
        <if test="type == 'in'">
            , last_in_time = NOW()
        </if>
        <if test="type == 'out'">
            , last_out_time = NOW()
        </if>
        WHERE material_id = #{materialId} 
        AND warehouse_id = #{warehouseId}
    </update>

</mapper>
