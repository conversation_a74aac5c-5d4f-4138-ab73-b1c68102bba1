<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fadun.mapper.OutboundMapper">

    <!-- 分页查询出库单 -->
    <select id="selectOutboundPage" resultType="com.fadun.entity.OutboundOrder">
        SELECT 
            oo.id,
            oo.order_no,
            oo.warehouse_id,
            oo.outbound_type,
            oo.receiver_unit,
            oo.receiver_name,
            oo.receiver_phone,
            oo.total_quantity,
            oo.operator_id,
            oo.operator_name,
            oo.outbound_time,
            oo.status,
            oo.remark,
            oo.create_time,
            oo.update_time,
            oo.create_by,
            oo.update_by,
            w.warehouse_name,
            CASE 
                WHEN oo.status = 1 THEN '待出库'
                WHEN oo.status = 2 THEN '已出库'
                WHEN oo.status = 3 THEN '已取消'
                ELSE '未知'
            END as statusName
        FROM outbound_order oo
        LEFT JOIN warehouse w ON oo.warehouse_id = w.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (oo.order_no LIKE CONCAT('%', #{keyword}, '%') 
                     OR oo.receiver_unit LIKE CONCAT('%', #{keyword}, '%')
                     OR oo.receiver_name LIKE CONCAT('%', #{keyword}, '%')
                     OR oo.operator_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="outboundType != null and outboundType != ''">
                AND oo.outbound_type = #{outboundType}
            </if>
            <if test="status != null">
                AND oo.status = #{status}
            </if>
        </where>
        ORDER BY oo.create_time DESC, oo.id DESC
    </select>

    <!-- 根据条件查询出库记录（支持日期范围） -->
    <select id="selectOutboundRecords" resultType="com.fadun.entity.OutboundOrder">
        SELECT 
            oo.id,
            oo.order_no,
            oo.warehouse_id,
            oo.outbound_type,
            oo.receiver_unit,
            oo.receiver_name,
            oo.receiver_phone,
            oo.total_quantity,
            oo.operator_id,
            oo.operator_name,
            oo.outbound_time,
            oo.status,
            oo.remark,
            oo.create_time,
            oo.update_time,
            w.warehouse_name,
            CASE 
                WHEN oo.status = 1 THEN '待出库'
                WHEN oo.status = 2 THEN '已出库'
                WHEN oo.status = 3 THEN '已取消'
                ELSE '未知'
            END as statusName
        FROM outbound_order oo
        LEFT JOIN warehouse w ON oo.warehouse_id = w.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (oo.order_no LIKE CONCAT('%', #{keyword}, '%') 
                     OR oo.receiver_unit LIKE CONCAT('%', #{keyword}, '%')
                     OR oo.receiver_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="warehouseId != null">
                AND oo.warehouse_id = #{warehouseId}
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE(oo.outbound_time) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(oo.outbound_time) &lt;= #{endDate}
            </if>
            AND oo.status = 2
        </where>
        ORDER BY oo.outbound_time DESC, oo.id DESC
    </select>

    <!-- 根据ID查询出库单详情 -->
    <select id="selectOutboundDetailById" resultType="com.fadun.entity.OutboundOrder">
        SELECT 
            oo.id,
            oo.order_no,
            oo.warehouse_id,
            oo.outbound_type,
            oo.receiver_unit,
            oo.receiver_name,
            oo.receiver_phone,
            oo.total_quantity,
            oo.operator_id,
            oo.operator_name,
            oo.outbound_time,
            oo.status,
            oo.remark,
            oo.create_time,
            oo.update_time,
            oo.create_by,
            oo.update_by,
            w.warehouse_name,
            CASE 
                WHEN oo.status = 1 THEN '待出库'
                WHEN oo.status = 2 THEN '已出库'
                WHEN oo.status = 3 THEN '已取消'
                ELSE '未知'
            END as statusName
        FROM outbound_order oo
        LEFT JOIN warehouse w ON oo.warehouse_id = w.id
        WHERE oo.id = #{id}
    </select>

    <!-- 批量插入出库明细 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO outbound_detail (order_id, material_id, quantity, remark)
        VALUES
        <foreach collection="details" item="detail" separator=",">
            (#{detail.orderId}, #{detail.materialId}, #{detail.quantity}, #{detail.remark})
        </foreach>
    </insert>

    <!-- 统计出库概览信息 -->
    <select id="getOutboundStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalOutboundOrders,
            COUNT(CASE WHEN status = 1 THEN 1 END) as pendingOutboundOrders,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completedOutboundOrders,
            COUNT(CASE WHEN status = 3 THEN 1 END) as cancelledOutboundOrders,
            COALESCE(SUM(CASE WHEN status = 2 THEN total_quantity ELSE 0 END), 0) as totalOutboundQuantity,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayOutboundOrders,
            COUNT(CASE WHEN DATE(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as weeklyOutboundOrders,
            COUNT(CASE WHEN YEAR(create_time) = YEAR(CURDATE()) AND MONTH(create_time) = MONTH(CURDATE()) THEN 1 END) as monthlyOutboundOrders
        FROM outbound_order
    </select>

    <!-- 按月统计出库数量 -->
    <select id="getMonthlyOutboundStats" resultType="map">
        SELECT 
            DATE_FORMAT(outbound_time, '%Y-%m') as month,
            COUNT(*) as orderCount,
            SUM(total_quantity) as totalQuantity
        FROM outbound_order 
        WHERE status = 2 
          AND outbound_time &gt;= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(outbound_time, '%Y-%m')
        ORDER BY month DESC
    </select>

    <!-- 按出库类型统计 -->
    <select id="getOutboundTypeStats" resultType="map">
        SELECT 
            outbound_type,
            COUNT(*) as orderCount,
            SUM(total_quantity) as totalQuantity,
            CASE 
                WHEN outbound_type = 'use' THEN '使用出库'
                WHEN outbound_type = 'transfer' THEN '调拨出库'
                WHEN outbound_type = 'scrap' THEN '报废出库'
                ELSE '其他出库'
            END as outboundTypeName
        FROM outbound_order 
        WHERE status = 2
        GROUP BY outbound_type
        ORDER BY orderCount DESC
    </select>

</mapper>
