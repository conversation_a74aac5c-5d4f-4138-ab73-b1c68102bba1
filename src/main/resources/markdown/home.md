# 法盾系统 API 文档

欢迎使用法盾系统 API 文档！

## 系统介绍

法盾系统是一个专业的战备物资管理平台，提供全面的物资台账、库存管理、入库出库、盘点维护等功能。

## 主要模块

### 📦 物资台账管理
- 物资信息CRUD
- 分类树形管理
- 库存状态查询
- 统计分析报表

### 📥 入库管理
- 一站式入库流程
- 入库记录查询
- 批量物资入库

### 📤 出库管理
- 出库单提交
- 库存充足性验证
- 出库记录跟踪

### 📊 库存盘点
- 盘点任务创建
- 实时盘点执行
- 差异分析报告

### 🔧 保养记录
- 设备保养计划
- 保养执行记录
- 保养统计分析

### 🏢 库室管理
- 库房基础信息
- 库室容量管理
- 物资分布查询

## 开发规范

请参考系统开发规范文档了解详细的开发标准和最佳实践。

## 技术支持

如有问题，请联系技术支持团队。
